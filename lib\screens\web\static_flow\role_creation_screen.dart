import 'dart:developer';
import 'dart:math' as math;

import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:nsl/models/RoleCreatModel.dart';
import 'package:nsl/models/add_role_model.dart';
import 'package:nsl/models/roles/get_roles_list.dart';
import 'package:nsl/models/roles/inheritance_role_model.dart';
import 'package:nsl/models/roles/validate_department_model.dart';
import 'package:nsl/models/roles_expansion_model.dart';
import 'package:nsl/providers/object_creation_provider.dart';
import 'package:nsl/providers/roles_provider.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/accordion_controller.dart';
import 'package:nsl/screens/web/static_flow/extract_details_middle_static.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:provider/provider.dart';

class RoleRowData {
  final TextEditingController roleController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  String? selectedReportsTo;
  List<String> selectedItems = [];
  bool isValidated = false; // Track validation status for each role

  void dispose() {
    roleController.dispose();
    descriptionController.dispose();
  }
}

class RoleCreationScreen extends StatefulWidget {
  final String? sessionId;
  final String? userIntent;

  const RoleCreationScreen({
    super.key,
    this.sessionId,
    this.userIntent,
  });

  @override
  State<RoleCreationScreen> createState() => _RoleCreationScreenState();
}

class _RoleCreationScreenState extends State<RoleCreationScreen> {
  List<RoleRowData> roleRows = [];
  late AccordionController _accordionController;
  
  List<Role> rolesList = [];


  List<Role> inheritanceList=[];
  List<String> selectedDepartments = [];
  bool _showOnlyDepartmentsContent = false;
  List<Role> departmentList = [];
  bool _isAIMode = true; // true for AI (right), false for Manual Process (left)

  @override
  void initState() {
    super.initState();
    _accordionController = AccordionController();
    roleRows.add(RoleRowData());
        WidgetsBinding.instance.addPostFrameCallback((_) {
    fetchData();
    });
    _accordionController.addListener(() {
      setState(() {});
    });
  }
  void fetchData() async{
      final rolesProvider = Provider.of<ObjectCreationProvider>(context, listen: false);
    //  await rolesProvider.fetchDepartment('departments');
     await rolesProvider.fetchRoles('roles');
     setState(() {
       
     });
  }

  @override
  void dispose() {
    for (var row in roleRows) {
      row.dispose();
    }
    _accordionController.dispose();
    super.dispose();
  }

  void _addNewRow() {
    setState(() {
      roleRows.add(RoleRowData());
    });
  }

  void _removeRow(int index) {
    if (index > 0 && index < roleRows.length) {
      setState(() {
        roleRows[index].dispose();
        roleRows.removeAt(index);
      });
    }
  }

  // Method to clear all fields after successful publish
  void _clearAllFields() async {
    setState(() {
      // Clear all role rows and dispose controllers
      for (var row in roleRows) {
        row.dispose();
      }
      roleRows.clear();
      
      // Add a fresh empty row
      roleRows.add(RoleRowData());
      
      // Clear other lists
      rolesList.clear();
      inheritanceList.clear();
      departmentList.clear();
      selectedDepartments.clear();
      
      // Reset UI state
      _showOnlyDepartmentsContent = false;
      fetchData();
    });
    
    // Clear provider data
    final provider = Provider.of<ObjectCreationProvider>(context, listen: false);
    provider.validateRoleModel = null;
    provider.saveRoleModel = null;
    provider.validateInheritanceModel = null;
    provider.validateDepartmentModel = null;
    provider.saveValidDepartmentModel = null;
    provider.publishEntityModel = null;
    
    // Refresh roles data to include newly published roles
    await provider.fetchRoles('roles');
  }

  // Method to validate and save role data
  Future<void> _validateAndSaveRole(int index) async {
    final provider = Provider.of<ObjectCreationProvider>(context, listen: false);
    
    // Create role list from current role data
    List<RoleCreateModel> roleList = [];
    
    // Get data from the specific role row
    RoleRowData rowData = roleRows[index];
    
    // Create role model for validation
    RoleCreateModel roleModel = RoleCreateModel(
      roleName: rowData.roleController.text,
      description: rowData.descriptionController.text,
      reportsTo: rowData.selectedReportsTo ?? '',
      department: '', // Default department or get from context
      orgLevel: '', // Default org level or get from context
      inherits:"" //rowData.selectedItems.join(', '),
    );
    
    roleList.add(roleModel);
    
    try {
      // Add mode - validate multiple roles
      await provider.parseValidateAddRoleEntity(roleList);
      
      if (provider.validateRoleModel?.hasErrors == true) {
        await showDialog(
          context: context, 
          builder: (context) => roleValidationErrorDialog(
            context, "Role Configuration", provider.validateRoleModel
          ),
        );
      } else {
        // If validation passes, mark role as validated and add to the list
        if(provider.saveRoleModel?.success==true){
                  rowData.isValidated = true;
        
        for (var param in roleList) {
          rolesList.add(Role(
            roleName: param.roleName,
            roleConfiguration: RoleConfiguration(
              roleName: param.roleName,
              description: param.description,
              reportsTo: param.reportsTo,
              organizationLevel: "",
              department: "",
              inherits: param.inherits,
            ),
            departmentConfiguration: DepartmentConfiguration(
              departmentName: param.department,
              description: param.description,
              departmentHeadRole: param.department,
            ),
            roleInheritance: RoleInheritance(
              parentRole: param.roleName,
              inheritsRole: param.inherits,
            ),
          ));
        }
          // Show success message or feedback
        // ScaffoldMessenger.of(context).showSnackBar(
        //   SnackBar(
        //     content: Text('Role "${roleModel.roleName}" validated and saved successfully!'),
        //     backgroundColor: Colors.green[200],
        //   ),
        // );
        }else{
    // Show success message or feedback
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save Role'),
            backgroundColor: Colors.red,
          ),
        );
            // Show success message or feedback
        // ScaffoldMessenger.of(context).showSnackBar(
        //   SnackBar(
        //     content: Text(provider.saveRoleModel?),
        //     backgroundColor: Colors.green,
        //   ),
        // );
        }

        
        setState(() {
          // Trigger UI update
        });
        
      
      }
    } catch (e) {
      // Handle any errors during validation
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error validating role: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  int _calculateTotalWidgets() {
    int totalWidgets = 0;
    
    if (_showOnlyDepartmentsContent) {
      // When showing departments content
      totalWidgets += 1; // Condensed role row
    } else {
      // When showing full role rows
      for (int i = 0; i < roleRows.length; i++) {
        totalWidgets += 2; // Role field + Description field
        if (i > 0) {
          totalWidgets += 1; // Remove button for additional rows
        }
      }
      totalWidgets += 1; // Add button
      // Removed: Object details section (commented out in UI)
    }
    
    return totalWidgets;
  }

  @override
  Widget build(BuildContext context) {
    int currentRowNumber = 1;
    

    return Consumer<ObjectCreationProvider>(
      builder: (context,dataProvide,_) {
        return Scaffold(
          body: Column(
            children: [
              // Header widget spanning full width
              _buildExtractedDetailsHeader(),
              // Content area with sidebar and main content
              Expanded(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Left sidebar with numbering
                    Container(
                      width: 40,
                      height: double.infinity,
                      decoration: BoxDecoration(
                        color: const Color(0xFFF8F9FA),
                        border: Border(
                         
                          right: BorderSide(
                            color: Colors.grey[300]!,
                            width: 1.0,
                          ),
                        ),
                      ),
                      child: SingleChildScrollView(
                        child: Padding(
                          padding: const EdgeInsets.only(top: 20.0),
                          child: Column(
                            children: _buildNumberedRows(),
                          ),
                        ),
                      ),
                    ),
                    // Main content area
                    Expanded(
                      child: SingleChildScrollView(
                        child: Padding(
                          padding: const EdgeInsets.all(20.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                    // Show condensed role view when departments content is shown
                    if (_showOnlyDepartmentsContent)
                      _buildCondensedRoleRow(currentRowNumber++),
                    
                    // Show full role rows when not showing departments content
                    if (!_showOnlyDepartmentsContent) ...[
                      ...roleRows.asMap().entries.map((entry) {
                        int index = entry.key;
                        RoleRowData rowData = entry.value;
            
                        int roleRowNumber = currentRowNumber++;
                        int descriptionRowNumber = currentRowNumber++;
                        int? removeRowNumber;
                        if (index > 0) {
                          removeRowNumber = currentRowNumber++;
                        }
            
                        return Column(
                          children: [
                            _buildFormRow(
                              rowNumber: roleRowNumber,
                              child: Row(
                                children: [
                                  Expanded(
                                    flex: 3,
                                    child: _buildEditableFormField(
                                        context, "Enter Role","Role", rowData.roleController),
                                  ),
                                  const SizedBox(width: 20),
                                  Expanded(
                                    flex: 2,
                                    child: _buildEditableDropdownField(
                                        context,
                                        dataProvide,
                                        'Reports To',
                                        rowData.selectedReportsTo, (value) {
                                      setState(() {
                                        rowData.selectedReportsTo = value;
                                        log(rowData.selectedReportsTo.toString());
                                      });
                                    }),
                                  ),
                                  // const SizedBox(width: 20),
                                  // Expanded(
                                  //   flex: 2,
                                  //   child: SizedBox(
                                  //     height: 38,
                                  //     child: _multiSelectionDropDown(
                                  //         context,
                                  //         dataProvide,
                                  //         "Inherits",
                                  //         rowData.selectedItems),
                                  //   ),
                                  // ),
                                  const SizedBox(width: 20),
                                 
                                    GestureDetector(
                                      onTap: () => _validateAndSaveRole(index),
                                      child: Container(
                                        width: 28,
                                        height: 28,
                                        decoration: const BoxDecoration(
                                          color: Color(0xFF007AFF),
                                          shape: BoxShape.circle,
                                        ),
                                        child: const Icon(
                                          Icons.check,
                                          color: Colors.white,
                                          size: 20,
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                            _buildFormRow(
                              rowNumber: descriptionRowNumber,
                              child: _buildEditableFormField(
                                  context,"Enter Description", "Description", rowData.descriptionController),
                            ),
                            if (index > 0)
                              _buildFormRow(
                                rowNumber: removeRowNumber!,
                                child: Row(
                                  children: [
                                    Expanded(child: Container()),
                                    GestureDetector(
                                      onTap: () => _removeRow(index),
                                      child: Container(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 10, vertical: 6),
                                        decoration: BoxDecoration(
                                          color: Colors.red[50],
                                          border: Border.all(color: Colors.red[300]!),
                                          borderRadius: BorderRadius.circular(4),
                                        ),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(
                                              Icons.delete_outline,
                                              color: Colors.red[600],
                                              size: 16,
                                            ),
                                            const SizedBox(width: 4),
                                            Text(
                                              'Remove',
                                              style: TextStyle(
                                                color: Colors.red[600],
                                                fontSize: 10,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          );
                        }),
                      _buildFormRow(
                        rowNumber: currentRowNumber++,
                        child: Row(
                          children: [
                            Expanded(child: Container()),
                            GestureDetector(
                              onTap: _addNewRow,
                              child: Container(
                                width: 30,
                                height: 30,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  border: Border.all(color: Colors.grey[300]!),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: const Icon(
                                  Icons.add,
                                  color: Color(0xFF007AFF),
                                  size: 20,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                    
                    // Show departments content when requested
                    // if (_showOnlyDepartmentsContent)
                    //   Container(
                    //     decoration: const BoxDecoration(
                    //       border: Border(
                    //         top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                    //       ),
                    //       color: Colors.white,
                    //     ),
                    //     child: _buildDepartmentsContent(context),
                    //   ),
                    
                    // // Show expansion tile when not showing departments content
                    // if (!_showOnlyDepartmentsContent)
                    //   _buildObjectDetailsSection(context, 'Role Configuration'),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // Bottom UI with Publish button - shows when validation and save are successful
              if (dataProvide.validateRoleModel != null && 
                  dataProvide.validateRoleModel?.hasErrors == false &&
                  dataProvide.saveRoleModel != null)
                _buildBottomPublishBar(context, dataProvide),
            ],
          ),
        );
      }
    );
  }

  Widget _buildBottomPublishBar(BuildContext context, ObjectCreationProvider provider) {
    return Container(
      width: double.infinity,
      height: 60,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: Colors.grey[300]!,
            width: 1.0,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 10.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          ElevatedButton(
            onPressed: () async {
              // Check if all roles are validated before publishing
              List<RoleRowData> unvalidatedRoles = roleRows.where((role) => !role.isValidated).toList();
              
              if (unvalidatedRoles.isNotEmpty) {
                // Show error message if there are unvalidated roles
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please validate all roles before publishing.'),
                    backgroundColor: Colors.red,
                    duration: Duration(seconds: 3),
                  ),
                );
                return;
              }
              
              // All roles are validated, proceed with publish
              await provider.publishRole(provider.validateRoleModel?.parsedData?.roleId??"");
              if (provider.publishEntitySuccessModel?.status=="success") {
                // Clear all related fields after successful publish
                _clearAllFields();
                
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Role published successfully!'),
                    backgroundColor: Colors.green,
                  ),
                );
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Failed to publish role. Please try again.'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF007AFF),
              foregroundColor: Colors.white,
              elevation: 0,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(6),
              ),
              minimumSize: const Size(100, 40),
            ),
            child: Text(
              'Publish',
              style: FontManager.getCustomStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExtractedDetailsHeader() {
    return Container(
      // height: 60,
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937), // Dark background color
        border: Border(
          bottom: BorderSide(
            color: Colors.grey[300]!,
            width: 1.0,
          ),
        ),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 20.0,vertical: 6),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Left side - "Extracted Details" text
          Text(
            'Extracted Details',
            style: FontManager.getCustomStyle(
              fontSize: 12,
              fontWeight: FontWeight.w700,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.white,
            ),
          ),
          // Right side - Toggle switch
          Row(
            children: [
              Text(
                'Manually Process',
                style: FontManager.getCustomStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.white,
                ),
              ),
              const SizedBox(width: 12),
              _buildToggleSwitch(),
              const SizedBox(width: 12),
              Text(
                'AI',
                style: FontManager.getCustomStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildToggleSwitch() {
    return GestureDetector(
      onTap: () {
        setState(() {
          _isAIMode = !_isAIMode;
        });
      },
      child: Container(
        width: 44,
        height: 24,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.white 
        ),
        child: Stack(
          children: [
            AnimatedPositioned(
              duration: const Duration(milliseconds: 200),
              curve: Curves.easeInOut,
              left: _isAIMode ? 20 : 2, // Position based on mode: right for AI, left for Manual
              top: 2,
              child: Container(
                width: 20,
                height: 20,
                decoration: const BoxDecoration(
                  color: Color(0xFF3B82F6),
                  shape: BoxShape.circle,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper method to build numbered rows for the sidebar
  List<Widget> _buildNumberedRows() {
    int totalWidgets = _calculateTotalWidgets();
    return List.generate(totalWidgets, (index) {
      return Container(
        height: _getRowHeight(index),
        alignment: Alignment.center,
        decoration: BoxDecoration(
         
        ),
        child: Container(
          width: 24,
          height: 20,
          alignment: Alignment.center,
          child: Text(
            '${index + 1}',
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w400,
              color: Colors.black,
              fontFamily: 'SF Pro Text',
            ),
          ),
        ),
      );
    });
  }

  // Helper method to calculate row height based on content type
  double _getRowHeight(int index) {
    if (_showOnlyDepartmentsContent) {
      return 76.0; // Condensed role row height to match content
      
    } else {
      // Calculate based on role rows - reduced heights
      int currentIndex = 0;
      
      for (int i = 0; i < roleRows.length; i++) {
        if (currentIndex == index) return 55.0; // Role field row (reduced from 67px)
        currentIndex++;
        if (currentIndex == index) return 55.0; // Description field row (reduced from 67px)
        currentIndex++;
        if (i > 0) {
          if (currentIndex == index) return 45.0; // Remove button row (reduced from 56px)
          currentIndex++;
        }
      }
      
      if (currentIndex == index) return 45.0; // Add button row (reduced from 56px)
      currentIndex++;
      if (currentIndex == index) return 50.0; // Object details section (reduced from 60px)
      
      return 50.0; // Default height (reduced)
    }
  }

  Widget _buildCondensedRoleRow(int rowNumber) {
    return _buildFormRow(
      rowNumber: rowNumber,
      child: InkWell(
        onTap: () {
          setState(() {
            _showOnlyDepartmentsContent = false;
          });
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  roleRows.isNotEmpty && roleRows[0].roleController.text.isNotEmpty
                      ? "Role: ${roleRows[0].roleController.text}"
                      : 'Role',
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodyMedium(context),
                    fontWeight: FontWeight.w600,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Container(
                width: 30,
                height: 30,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Icon(
                  Icons.add,
                  color: Color(0xFF007AFF),
                  size: 20,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFormRow({
    required int rowNumber,
    required Widget child,
    bool showNumbering = false, // Disable numbering within content area
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      child: child,
    );
  }

  Widget _buildEditableFormField(
      BuildContext context,String hintText, String label, TextEditingController controller) {
    return Row(
      children: [
        SizedBox(
          child: Text(
            "$label:",
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: label == "Role" ? FontWeight.w600 : FontWeight.w500,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
            overflow: TextOverflow.visible,
            maxLines: 2,
          ),
        ),
        SizedBox(width: 12),
        Expanded(
          child: SizedBox(
            height: 38,
            child: TextFormField(
              controller: controller,
              decoration: InputDecoration(
                hintText: hintText,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey[400]!),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey[400]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFF0058FF)),
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                filled: true,
                fillColor: Colors.white,
              ),
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodyMedium(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEditableDropdownField(
      BuildContext context,
      ObjectCreationProvider rolesProvide,
      String label,
      String? selectedValue,
      Function(String?) onChanged) {

    // Get dynamic options from roles provider and ensure uniqueness
    Set<String> uniqueOptions = <String>{};
    if ((rolesProvide.getRolesListModel?.data?.postgresData??[]).isNotEmpty) {
      for (PostgresDatum role in rolesProvide.getRolesListModel?.data?.postgresData??[]) {
        if (role.name != null && role.name!.isNotEmpty) {
          uniqueOptions.add(role.name!);
        }
      }
    }
    
    // Convert to list for dropdown and add "None" option at the beginning
    List<String> dynamicOptions = ['None', ...uniqueOptions.toList()];

    // Validate selected value - keep it null if not in options or if options are empty
    String? validatedValue;
    if (selectedValue != null && dynamicOptions.contains(selectedValue)) {
      validatedValue = selectedValue;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          height: 38,
          child: DropdownButtonFormField<String>(
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey[400]!),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey[400]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Color(0xFF0058FF)),
              ),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
              filled: true,
              fillColor: Colors.white,
            ),
            hint: Text(
              'Select $label',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodyMedium(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.grey[500],
              ),
            ),
            icon: Container(
              alignment: Alignment.centerRight,
              child: const Icon(
                Icons.keyboard_arrow_down,
                size: 24,
              ),
            ),
            iconSize: 24,
            isExpanded: true,
            value: validatedValue,
            onTap: () async {
              // Refresh roles data when dropdown is tapped
              await rolesProvide.fetchRoles('roles');
            },
            items: dynamicOptions.map((value) {
              return DropdownMenuItem(
                value: value,
                child: Container(
                  width: double.infinity,
                  child: Text(
                    value,
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodyMedium(context),
                      fontWeight: FontWeight.w400,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              );
            }).toList(),
            onChanged: onChanged,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
        ),
      ],
    );
  }

  Widget _multiSelectionDropDown(
      context,
      ObjectCreationProvider rolesProvide,
      String label,
      List<String> selectedItems) {
    // Get dynamic options from roles provider and ensure uniqueness
    Set<String> uniqueOptions = <String>{};
    if ((rolesProvide.getRolesListModel?.data?.postgresData??[]).isNotEmpty) {
      for (PostgresDatum role in rolesProvide.getRolesListModel?.data?.postgresData??[]) {
        if (role.name != null && role.name!.isNotEmpty) {
          uniqueOptions.add(role.name!);
        }
      }
    }
    // Convert to list for dropdown
    List<String> dynamicOptions = uniqueOptions.toList();
    return DropdownButtonHideUnderline(
      child: DropdownButton2<String>(
        isExpanded: true,
        customButton: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade400),
            borderRadius: BorderRadius.circular(8),
            color: Colors.white,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: Text(
                  selectedItems.isEmpty
                      ? 'Select Roles'
                      : selectedItems.join(', '),
                  style: TextStyle(
                    fontSize: 14,
                    color:
                        selectedItems.isEmpty ? Colors.grey[500] : Colors.black,
                    overflow: TextOverflow.ellipsis,
                  ),
                  maxLines: 1,
                ),
              ),
              Icon(
                Icons.keyboard_arrow_down,
                size: 20,
                color: Colors.grey[600],
              ),
            ],
          ),
        ),
        value: null,
        onChanged: (_) {},
        items: dynamicOptions.map((item) {
          return DropdownMenuItem<String>(
            value: item,
            enabled: false,
            child: StatefulBuilder(
              builder: (context, menuSetState) {
                final isSelected = selectedItems.contains(item);
                return InkWell(
                  onTap: () {
                    setState(() {
                      if (isSelected) {
                        selectedItems.remove(item);
                      } else {
                        selectedItems.add(item);
                      }
                    });
                    menuSetState(() {});
                  },
                  child: Container(
                    height: 44,
                    padding: const EdgeInsets.symmetric(horizontal: 12.0),
                    child: Row(
                      children: [
                        Icon(
                          isSelected
                              ? Icons.check_box
                              : Icons.check_box_outline_blank,
                          color: isSelected ? Colors.blue : Colors.grey[400],
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            item,
                            style: const TextStyle(fontSize: 14),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          );
        }).toList(),
        buttonStyleData: const ButtonStyleData(
          padding: EdgeInsets.zero,
          width: double.infinity,
        ),
        menuItemStyleData: const MenuItemStyleData(
          padding: EdgeInsets.zero,
        ),
        dropdownStyleData: DropdownStyleData(
          maxHeight: 200,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(8)),
            boxShadow: [
              BoxShadow(
                color: Color(0x1A000000),
                blurRadius: 8,
                offset: Offset(0, 2),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildObjectExpansionPanel(BuildContext context, String objectTitle) {
    bool isExpanded = true;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 1),
      decoration: BoxDecoration(
        color: Colors.white,
      ),
      child: Theme(
        data: Theme.of(context).copyWith(
          dividerColor: Colors.transparent,
        ),
        child: ListTileTheme(
          dense: true,
          child: ExpansionTile(
            initiallyExpanded: true,
            tilePadding: const EdgeInsets.symmetric(horizontal: 4, vertical: 0),
            childrenPadding: EdgeInsets.zero,
            trailing: Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color:
                    isExpanded ? const Color(0xFF0058FF) : Colors.transparent,
              ),
              child: Icon(
                isExpanded
                    ? Icons.keyboard_arrow_up
                    : Icons.keyboard_arrow_down,
                color: isExpanded ? Colors.white : Colors.grey[600],
                size: 20,
              ),
            ),
            title: Row(
              children: [
                Expanded(
                  child: Text(
                    objectTitle,
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleMedium(context),
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                      fontWeight: FontWeight.w600,
                      height: 1.2,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                HoverBellIcon(
                  onTap: () {},
                ),
              ],
            ),
            children: [
              _buildObjectDetailsSection(context, objectTitle),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildObjectDetailsSection(BuildContext context, String objectTitle) {
    return Container(
      margin: const EdgeInsets.fromLTRB(12, 0, 4, 0),
      child: Column(
        children: [
          _buildSimpleAccordionItem(
            context,
            'Departments',
            'Completed',
            '25 Atributes',
            const Color(0xFFD1FAE5),
            const Color(0xFF065F46),
            onTap: () {
              setState(() {
                _showOnlyDepartmentsContent = true;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSimpleAccordionItem(
    BuildContext context,
    String title,
    String status,
    String count,
    Color backgroundColor,
    Color textColor, {
    VoidCallback? onTap,
  }) {
    final isExpanded = _accordionController.isPanelExpanded('simple_$title');

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 2),
      decoration: BoxDecoration(
        border: Border.all(
          color: isExpanded ? const Color(0xFF0058FF) : const Color(0xFFE5E7EB),
          width: isExpanded ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(4),
        color: Colors.white,
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              if (onTap != null) {
                onTap();
              } else {
                _accordionController.togglePanel('simple_$title');
              }
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              child: Row(
                children: [
                  Expanded(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Flexible(
                          flex: 3,
                          child: Text(
                            title,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodyMedium(context),
                              fontWeight: isExpanded
                                  ? FontWeight.w600
                                  : FontWeight.w300,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                            overflow: TextOverflow.visible,
                            maxLines: 2,
                          ),
                        ),
                        const SizedBox(width: 18),
                        Flexible(
                          flex: 10,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: backgroundColor,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              status,
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.labelSmall(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: textColor,
                              ),
                              overflow: TextOverflow.visible,
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 12),
                  Container(
                    width: 150,
                    alignment: Alignment.centerRight,
                    child: Text(
                      count,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.labelSmall(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  AnimatedRotation(
                    turns: isExpanded ? 0.5 : 0.0,
                    duration: const Duration(milliseconds: 200),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.grey[600],
                      size: 16,
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (isExpanded) ...[
            Container(
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
                color: Colors.white,
              ),
              child: _buildDepartmentsContent(context),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDepartmentsContent(BuildContext context) {
    
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(6),
                topRight: Radius.circular(6),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Text(
                      'Departments',
                      style: FontManager.getCustomStyle(
                        fontSize: 16,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: const Color(0xFFD1FAE5),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'Completed',
                        style: FontManager.getCustomStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: const Color(0xFF065F46),
                        ),
                      ),
                    ),
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ElevatedButton(
                      onPressed: () => _showAddDepartmentModal(context),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF007AFF),
                        elevation: 0,
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4),
                        ),
                        minimumSize: const Size(0, 28),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.add,
                            size: 14,
                            color: Colors.white,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Add Department',
                            style: FontManager.getCustomStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            '25 Attributes',
                            style: FontManager.getCustomStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.grey[600],
                            ),
                          ),
                          // const SizedBox(width: 4),
                          // Icon(
                          //   Icons.keyboard_arrow_down,
                          //   size: 16,
                          //   color: Colors.grey[600],
                          // ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Container(
            height: 1,
            color: const Color(0xFFE5E7EB),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: const BoxDecoration(
              color: Color(0xFFF9FAFB),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 3,
                  child: Text(
                    'DEPARTMENT NAME',
                    style: FontManager.getCustomStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: const Color(0xFF6B7280),
                    ),
                  ),
                ),
                Expanded(
                  flex: 4,
                  child: Text(
                    'DESCRIPTION',
                    style: FontManager.getCustomStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: const Color(0xFF6B7280),
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'ROLES',
                    style: FontManager.getCustomStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: const Color(0xFF6B7280),
                    ),
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: Text(
                    'DEPARTMENT HEAD ROLE',
                    style: FontManager.getCustomStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: const Color(0xFF6B7280),
                    ),
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: Text(
                    'PARENT DEPARTMENT',
                    style: FontManager.getCustomStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: const Color(0xFF6B7280),
                    ),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    'ACTIONS',
                    style: FontManager.getCustomStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: const Color(0xFF6B7280),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: const BoxDecoration(
              color: Colors.white,
              border: Border(
                bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
              ),
            ),
            child: departmentList.isEmpty 
              ? const SizedBox.shrink()
              : ListView.builder(
                  itemCount: departmentList.length,
                  shrinkWrap: true,
                  itemBuilder: (context, index) {
                    return Row(
                  children: [
                    Expanded(
                      flex: 3,
                      child: Text(
                        departmentList[index].departmentConfiguration?.departmentName??"",
                        style: FontManager.getCustomStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 4,
                      child: Text(
                       departmentList[index].departmentConfiguration?.description??"",
                        style: FontManager.getCustomStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: Text(
                        "Role: ${roleRows.isNotEmpty ? roleRows[0].roleController.text : ''}",
                        style: FontManager.getCustomStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 3,
                      child: Text(
                        departmentList[index].departmentConfiguration?.departmentHeadRole??"",
                        style: FontManager.getCustomStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 3,
                      child: Text(
                        departmentList[index].departmentConfiguration?.parentDepartment??"",
                         style: FontManager.getCustomStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Row(
                        children: [
                          InkWell(
                            onTap: () {},
                            child: Container(
                              padding: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                color: const Color(0xFFFEF2F2),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: const Icon(
                                Icons.delete_outline,
                                size: 16,
                                color: Color(0xFFEF4444),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

// add role modal popup
  void _showAddDepartmentModal(BuildContext context) {
    // Create local controllers for this modal
    final TextEditingController departmentNameController = TextEditingController();
    final TextEditingController descriptionController = TextEditingController();
    String? selectedDepartmentHead;
    String? selectedParentDepartment = 'None';
    List<String> selectedRoles = [];
    // List<String> selectedItems = [];
    List<DeptCreateModel> deptModelList=[];
    

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, buildState) {
            return Consumer2<ObjectCreationProvider, RolesProvider>(
              builder: (context,data,rolesProvide,_) {
                // Get dynamic options from roles provider and ensure uniqueness
                Set<String> uniqueRoleOptions = <String>{};
                if (rolesProvide.roles.isNotEmpty) {
                  for (var role in rolesProvide.roles) {
                    if (role.name != null && role.name!.isNotEmpty) {
                      uniqueRoleOptions.add(role.name!);
                    }
                  }
                }
                // Convert to list for dropdown
                List<String> dynamicRoleOptions = uniqueRoleOptions.toList();
                return Dialog(
                  backgroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Container(
                    width: 560,
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Color(0x1A000000),
                          blurRadius: 16,
                          offset: Offset(0, 4),
                          spreadRadius: 0,
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Add Department',
                              style: FontManager.getCustomStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.black,
                              ),
                            ),
                            IconButton(
                              onPressed: () => Navigator.of(context).pop(),
                              icon: const Icon(Icons.close),
                              iconSize: 20,
                              color: Colors.grey[600],
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(),
                            ),
                          ],
                        ),
                        const SizedBox(height: 24),
                
                        // Department Name Field
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Department Name',
                              style: FontManager.getCustomStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.black,
                              ),
                            ),
                            const SizedBox(height: 8),
                            SizedBox(
                              height: 40,
                              child: TextFormField(
                                controller: departmentNameController,
                                decoration: InputDecoration(
                                  hintText: 'Enter Department Name',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(color: Colors.grey[300]!),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(color: Colors.grey[300]!),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: const BorderSide(color: Color(0xFF3B82F6)),
                                  ),
                                  contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                                  filled: true,
                                  fillColor: Colors.white,
                                ),
                                style: FontManager.getCustomStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: Colors.black,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                
                        // Description Field
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Description',
                              style: FontManager.getCustomStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.black,
                              ),
                            ),
                            const SizedBox(height: 8),
                            SizedBox(
                              height: 40,
                              child: TextFormField(
                                controller: descriptionController,
                                decoration: InputDecoration(
                                  hintText: 'Enter Description',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(color: Colors.grey[300]!),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(color: Colors.grey[300]!),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: const BorderSide(color: Color(0xFF3B82F6)),
                                  ),
                                  contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                                  filled: true,
                                  fillColor: Colors.white,
                                ),
                                style: FontManager.getCustomStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: Colors.black,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                
                        // Select Roles and Department Head Role Row
                        Row(
                          children: [
                            // Select Roles
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Select Roles',
                                    style: FontManager.getCustomStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      fontFamily: FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  SizedBox(
                                    height: 40,
                                    child: DropdownButtonHideUnderline(
                                      child: DropdownButton2<String>(
                                        isExpanded: true,
                                        customButton: Container(
                                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
                                          decoration: BoxDecoration(
                                            border: Border.all(color: Colors.grey[300]!),
                                            borderRadius: BorderRadius.circular(8),
                                            color: Colors.white,
                                          ),
                                          child: Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            crossAxisAlignment: CrossAxisAlignment.center,
                                            children: [
                                              Expanded(
                                                child: Text(
                                                  selectedRoles.isEmpty
                                                      ? 'None'
                                                      : selectedRoles.join(', '),
                                                  style: TextStyle(
                                                    fontSize: 14,
                                                    color: selectedRoles.isEmpty ? Colors.grey[500] : Colors.black,
                                                    overflow: TextOverflow.ellipsis,
                                                  ),
                                                  maxLines: 1,
                                                ),
                                              ),
                                              Icon(
                                                Icons.keyboard_arrow_down,
                                                size: 20,
                                                color: Colors.grey[600],
                                              ),
                                            ],
                                          ),
                                        ),
                                        value: null,
                                        onChanged: (_) {},
                                        items: dynamicRoleOptions.map((item) {
                                          return DropdownMenuItem<String>(
                                            value: item,
                                            enabled: false,
                                            child: StatefulBuilder(
                                              builder: (context, menuSetState) {
                                                final isSelected = selectedRoles.contains(item);
                                                return InkWell(
                                                  onTap: () {
                                                    buildState(() {
                                                      if (isSelected) {
                                                        selectedRoles.remove(item);
                                                      } else {
                                                        selectedRoles.add(item);
                                                      }
                                                    });
                                                    menuSetState(() {});
                                                  },
                                                  child: Container(
                                                    height: 44,
                                                    padding: const EdgeInsets.symmetric(horizontal: 12.0),
                                                    child: Row(
                                                      children: [
                                                        Icon(
                                                          isSelected
                                                              ? Icons.check_box
                                                              : Icons.check_box_outline_blank,
                                                          color: isSelected ? Colors.blue : Colors.grey[400],
                                                        ),
                                                        const SizedBox(width: 12),
                                                        Expanded(
                                                          child: Text(
                                                            item,
                                                            style: const TextStyle(fontSize: 14),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                );
                                              },
                                            ),
                                          );
                                        }).toList(),
                                        buttonStyleData: const ButtonStyleData(
                                          padding: EdgeInsets.zero,
                                          width: double.infinity,
                                        ),
                                        menuItemStyleData: const MenuItemStyleData(
                                          padding: EdgeInsets.zero,
                                        ),
                                        dropdownStyleData: DropdownStyleData(
                                          maxHeight: 200,
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.all(Radius.circular(8)),
                                            boxShadow: [
                                              BoxShadow(
                                                color: Color(0x1A000000),
                                                blurRadius: 8,
                                                offset: Offset(0, 2),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 16),
                            // Department Head Role
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Department Head Role',
                                    style: FontManager.getCustomStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      fontFamily: FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  SizedBox(
                                    height: 40,
                                    child: DropdownButtonFormField<String>(
                                      decoration: InputDecoration(
                                        border: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(8),
                                          borderSide: BorderSide(color: Colors.grey[300]!),
                                        ),
                                        enabledBorder: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(8),
                                          borderSide: BorderSide(color: Colors.grey[300]!),
                                        ),
                                        focusedBorder: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(8),
                                          borderSide: const BorderSide(color: Color(0xFF3B82F6)),
                                        ),
                                        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                                        filled: true,
                                        fillColor: Colors.white,
                                      ),
                                      hint: Text(
                                        'None',
                                        style: FontManager.getCustomStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w400,
                                          fontFamily: FontManager.fontFamilyTiemposText,
                                          color: Colors.grey[500],
                                        ),
                                      ),
                                      icon: const Icon(Icons.keyboard_arrow_down, size: 20),
                                      isExpanded: true,
                                      value: selectedDepartmentHead,
                                      items: (['None'] + dynamicRoleOptions).map((String value) {
                                        return DropdownMenuItem<String>(
                                          value: value,
                                          child: Text(
                                            value,
                                            style: FontManager.getCustomStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w400,
                                              fontFamily: FontManager.fontFamilyTiemposText,
                                              color: Colors.black,
                                            ),
                                          ),
                                        );
                                      }).toList(),
                                      onChanged: (value) {
                                        buildState(() {
                                          selectedDepartmentHead = value;
                                        });
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                
                        // Parent Department Field
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Parent Department',
                              style: FontManager.getCustomStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.black,
                              ),
                            ),
                            const SizedBox(height: 8),
                            SizedBox(
                              height: 40,
                              child: DropdownButtonFormField<String>(
                                decoration: InputDecoration(
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(color: Colors.grey[300]!),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(color: Colors.grey[300]!),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: const BorderSide(color: Color(0xFF3B82F6)),
                                  ),
                                  contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                                  filled: true,
                                  fillColor: Colors.white,
                                ),
                                hint: Text(
                                  'None',
                                  style: FontManager.getCustomStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400,
                                    fontFamily: FontManager.fontFamilyTiemposText,
                                    color: Colors.grey[500],
                                  ),
                                ),
                                icon: const Icon(Icons.keyboard_arrow_down, size: 20),
                                isExpanded: true,
                                value: selectedParentDepartment,
                                items: [
                                  'None',
                                  'Engineering',
                                  'Sales',
                                  'Product',
                                  'Executive',
                                  'Technology',
                                  'Finance',
                                  'Human Resources',
                                ].map((String value) {
                                  return DropdownMenuItem<String>(
                                    value: value,
                                    child: Text(
                                      value,
                                      style: FontManager.getCustomStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w400,
                                        fontFamily: FontManager.fontFamilyTiemposText,
                                        color: Colors.black,
                                      ),
                                    ),
                                  );
                                }).toList(),
                                onChanged: (value) {
                                  buildState(() {
                                    selectedParentDepartment = value;
                                  });
                                },
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 32),
                
                        // Action Buttons
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            OutlinedButton(
                              onPressed: () => Navigator.of(context).pop(),
                              style: OutlinedButton.styleFrom(
                                side: const BorderSide(color: Color(0xFFD1D5DB)),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 20,
                                  vertical: 10,
                                ),
                                backgroundColor: Colors.white,
                              ),
                              child: Text(
                                'Cancel',
                                style: FontManager.getCustomStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: const Color(0xFF374151),
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            ElevatedButton(
                              onPressed: () async{
                                   deptModelList.add(DeptCreateModel(
                                              department: selectedParentDepartment ?? '',
                                               deptHeadRole: selectedDepartmentHead ?? '',
                                               deptName: departmentNameController.text,
                                               description: descriptionController.text,
                                               role: selectedRoles.join(', ')
                                            ));
                                List<DeptCreateModel> temp = [];
                                for(int i=0;i<deptModelList.length;i++){
                                                     temp.add(DeptCreateModel(deptName: deptModelList[i].deptName, description: deptModelList[i].description, 
                                                     deptHeadRole:i==0?'[None]': deptModelList[i].deptHeadRole, department: deptModelList[i].department,role: selectedRoles.join(', ')));
                                                  }
                                                           final provider =
                                                Provider.of<ObjectCreationProvider>(context, listen: false);
                                                await  provider.parseValidateDeptEntity(temp);
                                          
                                                if(data.validateDepartmentModel?.isValid==false){
                                                  Navigator.of(context).pop();
                                                  await showDialog(context: context, builder: (context) => departmentValidationErrorDialog(
                                                    context,"Department", data.validateDepartmentModel
                                                  ),);
                                                        
                                                }else{
                                                  if(data.saveValidDepartmentModel?.success==true){
                                                    for(var  param in deptModelList){
                                                    departmentList.add(Role(roleName: param.deptName, departmentConfiguration: DepartmentConfiguration(departmentName: param.deptName, description: param.description, departmentHeadRole: param.deptHeadRole,parentDepartment: param.department,role: selectedRoles.join(', '))));
                                                    }
                                                    Navigator.of(context).pop();
                                                    setState(() {
                                                      
                                                    });
                                                  }
                                                }
                                                
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xFF3B82F6),
                                elevation: 0,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 20,
                                  vertical: 10,
                                ),
                              ),
                              child: Text(
                                'Apply This',
                                style: FontManager.getCustomStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              }
            );
          },
        );
      },
    );
  }

  Widget departmentValidationErrorDialog(context, title, ValidateDepartmentModel? model) {
    // final provider =
    //     Provider.of<ObjectCreationProvider>(context, listen: false);
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        width: math.min(MediaQuery.of(context).size.width * 0.8, 534),
        height: math.min(MediaQuery.of(context).size.height * 0.8, 600),
        child: Column(
          children: [
            // Header with bottom border
            Container(
              padding: const EdgeInsets.only(
                  bottom: 14, top: 14, left: 24, right: 24),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '$title Validation Error',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodyLarge(context),
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyInter,
                      color: Colors.black,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                    iconSize: 24,
                    color: Colors.grey[600],
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ),
            Expanded(
                child: Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 24, vertical: 8.0),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Purpose',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyLarge(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(
                      height: AppSpacing.xs,
                    ),
                    Text(
                      "We encountered one or more issues. Please review the following:",
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.titleSmall(context),
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(
                      height: AppSpacing.xs,
                    ),
                    Expanded(
                      child: Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: Color(0xffF8FAFC),
                          border: Border.all(color: Color(0xffE5E7EA)),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        padding: EdgeInsets.all(AppSpacing.sm),
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            spacing: AppSpacing.xs,
                            children: [
                              if (model?.success !=
                                  null)
                                Text(
                                  model?.failureReason??"",
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              if ((model?.dependencyErrors??[]).isNotEmpty)
                                for (var element in model
                                    !.dependencyErrors??[])
                                  Text(
                                    "${element ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues
                                      ?.warnings !=
                                  null)
                                for (var element in model?.issues?.warnings??[])
                                  Text(
                                    "${element ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues
                                      ?.exceptions !=
                                  null)
                                for (var element in model?.issues?.exceptions??[])
                                  Text(
                                    "${element ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues
                                      ?.validationErrors !=
                                  null)
                                for (var element in model?.issues?.validationErrors??[])
                                  Text(
                                    "${element["message"] ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues
                                      ?.dependencyErrors !=
                                  null)
                                for (DependencyError element in model?.issues?.dependencyErrors??[])
                                  Text(
                                    "${element.message?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues
                                      ?.uniquenessIssues !=
                                  null)
                                for (var element in model?.issues?.uniquenessIssues??[])
                                  Text(
                                    "${element['message'] ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues
                                      ?.parsingIssues !=
                                  null)
                                for (var element in model?.issues?.parsingIssues??[])
                                  Text(
                                    "${element["message"] ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues
                                      ?.mongoErrors !=
                                  null)
                                for (var element in model?.issues?.mongoErrors??[])
                                  Text(
                                    "${element["message"] ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues
                                      ?.postgresErrors !=
                                  null)
                                for (var element in model?.issues?.postgresErrors??[])
                                  Text(
                                    '${element["message"] ?? ''}\n',
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model
                                          ?.validationResult?.structureErrors !=
                                      null &&
                                 (model?.validationResult?.structureErrors??[])
                                      .isNotEmpty)
                                Text(
                                  (model?.validationResult?.structureErrors??[])
                                      .join('\n'),
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              if (model
                                          ?.validationResult
                                          ?.requiredFieldErrors !=
                                      null &&
                                  (model
                                      ?.validationResult?.requiredFieldErrors??[])
                                      .isNotEmpty)
                                Text(
                                  (model
                                      ?.validationResult?.requiredFieldErrors??[])
                                      .join('\n'),
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              if (model?.validationResult?.dataTypeErrors !=
                                      null &&
                                  (model?.validationResult?.dataTypeErrors??[]).isNotEmpty)
                                Text(
                                 (model?.validationResult?.dataTypeErrors??[])
                                      .join('\n'),
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              if (model
                                          ?.validationResult?.customErrors !=
                                      null &&
                                  (model?.validationResult?.customErrors??[])
                                      .isNotEmpty)
                                Text(
                                  (model?.validationResult?.customErrors??[])
                                      .join('\n'),
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ]),
            )),
            // Footer with top border
            Container(
              padding: const EdgeInsets.only(
                  bottom: 14, top: 14, left: 24, right: 24),
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF0058FF),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      'Proceed',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyMedium(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

   Widget roleValidationErrorDialog(context, title, ValidateAddRoleModel? model) {
    // final provider =
    //     Provider.of<ObjectCreationProvider>(context, listen: false);
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        width: math.min(MediaQuery.of(context).size.width * 0.8, 534),
        height: math.min(MediaQuery.of(context).size.height * 0.8, 600),
        child: Column(
          children: [
            // Header with bottom border
            Container(
              padding: const EdgeInsets.only(
                  bottom: 14, top: 14, left: 24, right: 24),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '$title Validation Error',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodyLarge(context),
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyInter,
                      color: Colors.black,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                    iconSize: 24,
                    color: Colors.grey[600],
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ),
            Expanded(
                child: Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 24, vertical: 8.0),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Purpose',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyLarge(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(
                      height: AppSpacing.xs,
                    ),
                    Text(
                      "We encountered one or more issues. Please review the following:",
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.titleSmall(context),
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(
                      height: AppSpacing.xs,
                    ),
                    Expanded(
                      child: Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: Color(0xffF8FAFC),
                          border: Border.all(color: Color(0xffE5E7EA)),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        padding: EdgeInsets.all(AppSpacing.sm),
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            spacing: AppSpacing.xs,
                            children: [
                              if (model?.success !=
                                  null)
                                Text(
                                  model?.failureReason??"",
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              if ((model?.dependencyErrors??[]).isNotEmpty)
                                for (var element in model
                                    !.dependencyErrors??[])
                                  Text(
                                    "${element ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues
                                      ?.warnings !=
                                  null)
                                for (var element in model?.issues?.warnings??[])
                                  Text(
                                    "${element ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues
                                      ?.exceptions !=
                                  null)
                                for (var element in model?.issues?.exceptions??[])
                                  Text(
                                    "${element ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues
                                      ?.validationErrors !=
                                  null)
                                for (var element in model?.issues?.validationErrors??[])
                                  Text(
                                    "${element["message"] ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues
                                      ?.dependencyErrors !=
                                  null)
                                for (RoleDependencyError element in model?.issues?.dependencyErrors??[])
                                  Text(
                                    "${element.message?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues
                                      ?.uniquenessIssues !=
                                  null)
                                for (var element in model?.issues?.uniquenessIssues??[])
                                  Text(
                                    "${element['message'] ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues
                                      ?.parsingIssues !=
                                  null)
                                for (var element in model?.issues?.parsingIssues??[])
                                  Text(
                                    "${element["message"] ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues
                                      ?.mongoErrors !=
                                  null)
                                for (var element in model?.issues?.mongoErrors??[])
                                  Text(
                                    "${element["message"] ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues
                                      ?.postgresErrors !=
                                  null)
                                for (var element in model?.issues?.postgresErrors??[])
                                  Text(
                                    '${element["message"] ?? ''}\n',
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model
                                          ?.validationResult?.structureErrors !=
                                      null &&
                                 (model?.validationResult?.structureErrors??[])
                                      .isNotEmpty)
                                Text(
                                  (model?.validationResult?.structureErrors??[])
                                      .join('\n'),
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              if (model
                                          ?.validationResult
                                          ?.requiredFieldErrors !=
                                      null &&
                                  (model
                                      ?.validationResult?.requiredFieldErrors??[])
                                      .isNotEmpty)
                                Text(
                                  (model
                                      ?.validationResult?.requiredFieldErrors??[])
                                      .join('\n'),
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              if (model?.validationResult?.dataTypeErrors !=
                                      null &&
                                  (model?.validationResult?.dataTypeErrors??[]).isNotEmpty)
                                Text(
                                 (model?.validationResult?.dataTypeErrors??[])
                                      .join('\n'),
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              if (model
                                          ?.validationResult?.customErrors !=
                                      null &&
                                  (model?.validationResult?.customErrors??[])
                                      .isNotEmpty)
                                Text(
                                  (model?.validationResult?.customErrors??[])
                                      .join('\n'),
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ]),
            )),
            // Footer with top border
            Container(
              padding: const EdgeInsets.only(
                  bottom: 14, top: 14, left: 24, right: 24),
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF0058FF),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      'Proceed',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyMedium(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class HoverBellIcon extends StatefulWidget {
  final VoidCallback onTap;

  const HoverBellIcon({
    super.key,
    required this.onTap,
  });

  @override
  State<HoverBellIcon> createState() => _HoverBellIconState();
}

class _HoverBellIconState extends State<HoverBellIcon> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: InkWell(
        onTap: widget.onTap,
        child: Container(
          padding: const EdgeInsets.all(4.0),
          child: Icon(
            Icons.notifications_outlined,
            size: 18,
            color: isHovered ? Colors.red : Colors.grey[600],
          ),
        ),
      ),
    );
  }



}
