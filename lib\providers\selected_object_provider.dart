import 'package:flutter/foundation.dart';

/// Data structure to hold object and its attributes
class SelectedObjectData {
  final Map<String, dynamic> object;
  final List<String> attributes;
  final String id; // Unique identifier for the object

  SelectedObjectData({
    required this.object,
    required this.attributes,
    required this.id,
  });

  /// Create a unique ID from object data
  static String generateId(Map<String, dynamic> object) {
    return '${object['entityId'] ?? object['name'] ?? object['displayName']}_${DateTime.now().millisecondsSinceEpoch}';
  }
}

/// Provider to manage selected object state for Input Stack Accordion
class SelectedObjectProvider extends ChangeNotifier {
  // Legacy single object support (for backward compatibility)
  Map<String, dynamic>? _selectedObject;
  List<String>? _selectedObjectAttributes;

  // New multiple objects support
  final List<SelectedObjectData> _selectedObjects = [];

  // Legacy getters (for backward compatibility)
  Map<String, dynamic>? get selectedObject => _selectedObject;
  List<String>? get selectedObjectAttributes => _selectedObjectAttributes;

  // New getters for multiple objects
  List<SelectedObjectData> get selectedObjects =>
      List.unmodifiable(_selectedObjects);
  bool get hasMultipleObjects => _selectedObjects.isNotEmpty;
  int get selectedObjectsCount => _selectedObjects.length;

  /// Legacy method - Set the selected object and its attributes (for backward compatibility)
  void setSelectedObject(Map<String, dynamic> object, List<String> attributes) {
    _selectedObject = object;
    _selectedObjectAttributes = attributes;
    notifyListeners();
  }

  /// Add a new object to the selected objects list
  void addSelectedObject(Map<String, dynamic> object, List<String> attributes) {
    final id = SelectedObjectData.generateId(object);

    // For individual attribute selections (single attribute), check for exact match
    // For full object selections (multiple attributes), check for base object match
    bool isIndividualAttribute = attributes.length == 1;

    int existingIndex = -1;

    if (isIndividualAttribute) {
      // For individual attributes, check for exact entityId match (which includes the attribute suffix)
      existingIndex = _selectedObjects
          .indexWhere((data) => data.object['entityId'] == object['entityId']);
    } else {
      // For full objects, check for base entityId/name match (without attribute suffix)
      existingIndex = _selectedObjects.indexWhere((data) {
        final dataEntityId = data.object['entityId']?.toString() ?? '';
        final objectEntityId = object['entityId']?.toString() ?? '';

        // Remove attribute suffix for comparison if it exists
        final dataBaseId = dataEntityId.contains('_attr')
            ? dataEntityId.substring(0, dataEntityId.lastIndexOf('_attr'))
            : dataEntityId;
        final objectBaseId = objectEntityId.contains('_attr')
            ? objectEntityId.substring(0, objectEntityId.lastIndexOf('_attr'))
            : objectEntityId;

        return dataBaseId == objectBaseId ||
            (data.object['name'] == object['name'] && object['name'] != null);
      });
    }

    if (existingIndex != -1) {
      // Update existing object
      _selectedObjects[existingIndex] = SelectedObjectData(
        object: object,
        attributes: attributes,
        id: _selectedObjects[existingIndex].id, // Keep the same ID
      );
    } else {
      // Add new object
      _selectedObjects.add(SelectedObjectData(
        object: object,
        attributes: attributes,
        id: id,
      ));
    }

    notifyListeners();
  }

  /// Remove an object by its ID
  void removeSelectedObject(String id) {
    _selectedObjects.removeWhere((data) => data.id == id);
    notifyListeners();
  }

  /// Clear all selected objects
  void clearAllSelectedObjects() {
    _selectedObjects.clear();
    notifyListeners();
  }

  /// Legacy method - Clear the selected object (for backward compatibility)
  void clearSelectedObject() {
    _selectedObject = null;
    _selectedObjectAttributes = null;
    notifyListeners();
  }

  /// Check if an object is currently selected (legacy support)
  bool get hasSelectedObject => _selectedObject != null;

  /// Get object data by ID
  SelectedObjectData? getObjectById(String id) {
    try {
      return _selectedObjects.firstWhere((data) => data.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Check if a specific object already exists in the selection
  bool hasObject(Map<String, dynamic> object) {
    return _selectedObjects.any((data) =>
        data.object['entityId'] == object['entityId'] ||
        (data.object['name'] == object['name'] && object['name'] != null));
  }

  /// Check if an individual attribute from an object already exists
  bool hasAttribute(Map<String, dynamic> object, String attribute) {
    final expectedEntityId =
        '${object['entityId'] ?? object['name']}_${attribute}_attr';
    return _selectedObjects
        .any((data) => data.object['entityId'] == expectedEntityId);
  }

  /// Get all individual attributes (single attribute selections)
  List<SelectedObjectData> get individualAttributes {
    return _selectedObjects
        .where((data) => data.attributes.length == 1)
        .toList();
  }

  /// Get all full objects (multiple attribute selections)
  List<SelectedObjectData> get fullObjects {
    return _selectedObjects
        .where((data) => data.attributes.length > 1)
        .toList();
  }
}
