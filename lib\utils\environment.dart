 import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:nsl/config/app_config.dart';

class Environment {
  Environment._privateConstructor() {
    // Initialize base URLs from env file or use defaults
    _transactionApiBaseUrl =
        AppConfig.transactionBaseUrl??'';
    _buildApiBaseUrl =
        AppConfig.buildBaseUrl ?? '';
    _chatApiBaseUrl =
        AppConfig.chatBaseUrl ?? '';
  }

  static final Environment _instance = Environment._privateConstructor();
  static Environment get instance => _instance;

  // Base URLs
  late final String _transactionApiBaseUrl;
  late final String _buildApiBaseUrl;
  late final String _chatApiBaseUrl;

  // Getter methods for base URLs
  String get transactionApiBaseUrl => _transactionApiBaseUrl;
  String get buildApiBaseUrl => _buildApiBaseUrl;
  String get chatApiBaseUrl => _chatApiBaseUrl;

  // Transaction API Endpoints
  String get transactionsUrl => '$_transactionApiBaseUrl/api/transactions';
  String get executeUrl => '$_transactionApiBaseUrl/api/execute';
  String get globalObjectivesUrl =>
      '$_transactionApiBaseUrl/api/v1/global-objectives/';
  String get transactionDetailsUrl =>
      '$_transactionApiBaseUrl/api/v1/my_transaction/transactionsdetails';
  String validateGoAvailableUrl(String goId) =>
      '$_transactionApiBaseUrl/api/v1/my_transaction/validateIsGoAvailableInMyTransaction?go_id=$goId';

  // Workflow API Endpoints
  String get workflowInstancesUrl =>
      '$_transactionApiBaseUrl/api/v1/workflows/instances';
  String workflowInstanceUrl(String instanceId) =>
      '$workflowInstancesUrl/$instanceId';
  String workflowStartUrl(String instanceId) =>
      '${workflowInstanceUrl(instanceId)}/start';
  String workflowExecuteUrl(String instanceId) =>
      '${workflowInstanceUrl(instanceId)}/execute';
  String workflowInputsUrl(String instanceId) =>
      '${workflowInstanceUrl(instanceId)}/inputs';

  // Build API Endpoints
  String get buildChatUrl => '$_buildApiBaseUrl/api/chat';
  String get solutionsUrl => '$_buildApiBaseUrl/api/solutions';
  String get deployUrl => '$_buildApiBaseUrl/api/deploy_by_id';

  // Chat API Endpoints
  String get chatCompletionsUrl => '$_chatApiBaseUrl/chat/completions';

  // API Keys
  String? get openAiApiKey => dotenv.env['API_KEY'];
}
