import 'package:dio/dio.dart';
import '../config/environment.dart';
import '../models/books/books_model.dart';
import '../models/books/chapter_model.dart';
import '../models/books/global_objective_model.dart';
import 'base_api_service.dart';
import 'auth_service.dart';
import '../utils/logger.dart';

/// Service for handling books-related API operations
class BooksService extends BaseApiService {
  // Updated to use the new API endpoint
  String get _baseUrl => Environment.transactionApiBaseUrl;
  static const String _booksEndpoint = '/api/v2/global_objectives/T1008/books';
  static const String _chaptersEndpoint = '/api/v2/global_objectives/books';
  static const String _objectivesEndpoint =
      '/api/v2/global_objectives/chapters';

  // Auth service for getting user data
  final AuthService _authService = AuthService();

  /// Fetch all books using the new API endpoint
  Future<List<BooksModel>> getAllBooks() async {
    try {
      // Use the updated token from the curl command
      const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJVMjAiLCJ1c2VybmFtZSI6ImhyX21hbmFnZXIiLCJyb2xlcyI6WyJSMjQiXSwidGVuYW50X2lkIjoiVDEwMDgiLCJleHAiOjE3NTM4NTczNTN9.7LQXgWF3gLYqdbzf7Ue385JUliSiIlOik8sr4c1hFC8';
      
      Logger.info('Fetching books from new API endpoint');

      // Build the full URL for the new API
      final fullUrl = '$_baseUrl$_booksEndpoint';

      // Replicate the exact curl command - GET request with data body
      final requestData = {
        'tenant_id': 't001',
      };

      final response = await dio.request(
        fullUrl,
        options: Options(
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
        ),
        data: requestData,
      );

      Logger.info('Books fetched successfully: ${response.statusCode}');

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          // Parse the new response format
          final booksResponse = BooksResponse.fromJson(response.data);
          return booksResponse.books ?? [];
        } else {
          Logger.error('Unexpected response format: ${response.data}');
          throw Exception('Invalid response format');
        }
      } else {
        final errorMessage =
            response.data['message'] ?? 'Failed to fetch books';
        Logger.error('Failed to fetch books: $errorMessage');
        throw Exception(errorMessage);
      }
    } on DioException catch (e) {
      Logger.error('Dio error fetching books: ${e.message}');
      Logger.error('Response data: ${e.response?.data}');
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      Logger.error('Error fetching books: $e');
      rethrow;
    }
  }

  /// Fetch chapters for a specific book using the new API endpoint
  Future<List<ChapterModel>> getBookChapters({
    required String bookId,
    required String tenantId,
  }) async {
    try {
      Logger.info('Fetching chapters for book: $bookId, tenant: $tenantId');

      // Use the updated token from the curl command
      const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJVMjAiLCJ1c2VybmFtZSI6ImhyX21hbmFnZXIiLCJyb2xlcyI6WyJSMjQiXSwidGVuYW50X2lkIjoiVDEwMDgiLCJleHAiOjE3NTM4NTczNTN9.7LQXgWF3gLYqdbzf7Ue385JUliSiIlOik8sr4c1hFC8';

      final options = Options(
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      // Build the full URL using the new API endpoint format
      final fullUrl = '$_baseUrl/api/v2/global_objectives/T1008/books/$bookId/chapters';

      final response = await dio.get(
        fullUrl,
        options: options,
      );

      Logger.info('Book chapters fetched successfully: ${response.statusCode}');

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          // Parse the new response format
          final chaptersResponse = ChaptersResponse.fromJson(response.data);
          return chaptersResponse.chapters ?? [];
        } else {
          Logger.error('Unexpected response format: ${response.data}');
          throw Exception('Invalid response format');
        }
      } else {
        final errorMessage =
            response.data['message'] ?? 'Failed to fetch book chapters';
        Logger.error('Failed to fetch book chapters: $errorMessage');
        throw Exception(errorMessage);
      }
    } on DioException catch (e) {
      Logger.error('Dio error fetching book chapters: ${e.message}');
      Logger.error('Response data: ${e.response?.data}');
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      Logger.error('Error fetching book chapters: $e');
      rethrow;
    }
  }

  /// Fetch chapters for a specific book using current user's tenant
  Future<List<ChapterModel>> getBookChaptersWithUserTenant({
    required String bookId,
  }) async {
    try {
      // Get tenant ID from current user
      final savedAuthData = await _authService.getSavedAuthData();
      String tenantId = 'T1008'; // Updated to match new API

      if (savedAuthData.success && savedAuthData.data != null) {
        tenantId = savedAuthData.data!.user?.tenantId ?? 'T1008';
      }

      Logger.info('Using tenant ID: $tenantId for book: $bookId');

      return await getBookChapters(
        bookId: bookId,
        tenantId: tenantId,
      );
    } catch (e) {
      Logger.error('Error fetching book chapters with user tenant: $e');
      rethrow;
    }
  }

  /// Fetch objectives for a specific chapter
  Future<List<Objective>> getChapterObjectives({
    required String chapterId,
    required String tenantId,
  }) async {
    try {
      Logger.info(
          'Fetching objectives for chapter: $chapterId, tenant: $tenantId');

      // Get a valid token
      final token = await _authService.getValidToken();
      if (token == null) {
        Logger.error('No valid token available to fetch chapter objectives');
        throw Exception('Authentication required');
      }

      final queryParameters = {
        'tenant_id': tenantId,
      };

      final options = Options(
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      // Build the full URL: /api/v2/global_objectives/chapters/{chapter_id}/objectives
      final fullUrl = '$_baseUrl$_objectivesEndpoint/$chapterId/objectives';

      final response = await dio.get(
        fullUrl,
        queryParameters: queryParameters,
        options: options,
      );

      Logger.info(
          'Chapter objectives fetched successfully: ${response.statusCode}');

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          // Parse using GlobalObjectiveModel which contains objectives array
          final globalObjectiveModel =
              GlobalObjectiveModel.fromJson(response.data);
          return globalObjectiveModel.objectives ?? [];
        } else if (response.data is List) {
          // If response is directly a list of objectives
          final List<dynamic> objectivesJson = response.data;
          return objectivesJson
              .map((json) => Objective.fromJson(json))
              .toList();
        } else {
          Logger.error('Unexpected response format: ${response.data}');
          throw Exception('Invalid response format');
        }
      } else {
        final errorMessage =
            response.data['message'] ?? 'Failed to fetch chapter objectives';
        Logger.error('Failed to fetch chapter objectives: $errorMessage');
        throw Exception(errorMessage);
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        Logger.info('Received 401, attempting to refresh token and retry');

        // Get a fresh token
        final newToken = await _authService.getValidToken();
        if (newToken != null) {
          // Retry the request with the new token
          return await getChapterObjectives(
              chapterId: chapterId, tenantId: tenantId);
        }
      }

      Logger.error('Dio error fetching chapter objectives: ${e.message}');
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      Logger.error('Error fetching chapter objectives: $e');
      rethrow;
    }
  }

  /// Fetch objectives for a specific chapter using current user's tenant
  Future<List<Objective>> getChapterObjectivesWithUserTenant({
    required String chapterId,
  }) async {
    try {
      // Get tenant ID from current user
      final savedAuthData = await _authService.getSavedAuthData();
      String tenantId = 't001'; // Default fallback

      if (savedAuthData.success && savedAuthData.data != null) {
        tenantId = savedAuthData.data!.user?.tenantId ?? 't001';
      }

      Logger.info('Using tenant ID: $tenantId for chapter: $chapterId');

      return await getChapterObjectives(
        chapterId: chapterId,
        tenantId: tenantId,
      );
    } catch (e) {
      Logger.error('Error fetching chapter objectives with user tenant: $e');
      rethrow;
    }
  }

  /// Fetch all objectives using the new API endpoint
  Future<List<Objective>> getAllObjectives() async {
    try {
      Logger.info('Fetching all objectives from new API endpoint');

      // Use the updated token from the curl command
      const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJVMjAiLCJ1c2VybmFtZSI6ImhyX21hbmFnZXIiLCJyb2xlcyI6WyJSMjQiXSwidGVuYW50X2lkIjoiVDEwMDgiLCJleHAiOjE3NTM4NTczNTN9.7LQXgWF3gLYqdbzf7Ue385JUliSiIlOik8sr4c1hFC8';

      final options = Options(
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      // Build the full URL using the new API endpoint format
      final fullUrl = '$_baseUrl/api/v2/global_objectives/?tenant_id=T1008';

      final response = await dio.get(
        fullUrl,
        options: options,
      );

      Logger.info('All objectives fetched successfully: ${response.statusCode}');

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          // Parse the new response format
          final objectivesResponse = GlobalObjectiveModel.fromJson(response.data);
          return objectivesResponse.objectives ?? [];
        } else {
          Logger.error('Unexpected response format: ${response.data}');
          throw Exception('Invalid response format');
        }
      } else {
        final errorMessage =
            response.data['message'] ?? 'Failed to fetch objectives';
        Logger.error('Failed to fetch objectives: $errorMessage');
        throw Exception(errorMessage);
      }
    } on DioException catch (e) {
      Logger.error('Dio error fetching objectives: ${e.message}');
      Logger.error('Response data: ${e.response?.data}');
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      Logger.error('Error fetching objectives: $e');
      rethrow;
    }
  }
}
