import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:nsl/utils/logger.dart';
import '../config/environment.dart';

class NewFileUploadService {
  final Dio _dio = Dio();
  
  // New API endpoint
  String get _newApiUrl => Environment.fileUploadProcessUrl;

  // Method to pick a file (reusing the same logic)
  Future<PlatformFile?> pickFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.any,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        return result.files.first;
      }

      return null;
    } catch (e) {
      Logger.error('Error picking file: $e');
      return null;
    }
  }

  // Method to upload file using the new API
  Future<Map<String, dynamic>> uploadFileNewApi(PlatformFile file, {
    String strategy = 'auto',
    bool asyncProcessing = false,
  }) async {
    try {
      // Create form data with the new API structure
      FormData formData;

      if (kIsWeb) {
        // For web, use the bytes
        formData = FormData.fromMap({
          'file': MultipartFile.fromBytes(
            file.bytes!,
            filename: file.name,
          ),
          'strategy': strategy,
          'async_processing': asyncProcessing.toString(),
        });
      } else {
        // For mobile, use the file path
        formData = FormData.fromMap({
          'file': await MultipartFile.fromFile(
            file.path!,
            filename: file.name,
          ),
          'strategy': strategy,
          'async_processing': asyncProcessing.toString(),
        });
      }

      // Set up headers (no authorization needed based on the API docs)
      final headers = {
        'Content-Type': 'multipart/form-data',
      };

      Logger.info('Uploading file: ${file.name} using new API endpoint');

      // Make the API call
      final response = await _dio.post(
        _newApiUrl,
        data: formData,
        options: Options(headers: headers),
      );

      // Check if the response is successful
      if (response.statusCode == 200) {
        Logger.info('File uploaded successfully using new API: ${response.data}');

        final responseData = response.data;
        
        // Extract text content from the new API response structure
        String extractedText = '';
        if (responseData['result'] != null && responseData['result']['text_content'] != null) {
          extractedText = responseData['result']['text_content'];
        }

        Logger.info('Extracted text from new API: $extractedText');

        return {
          'success': true,
          'data': responseData,
          'file_name': file.name,
          'extracted_text': extractedText,
          'job_id': responseData['job_id'],
          'status': responseData['status'],
          'processing_time': responseData['processing_time'],
          'confidence_scores': responseData['confidence_scores'],
          'tools_used': responseData['tools_used'],
          'metadata': responseData['result']?['metadata'],
        };
      } else {
        final errorMessage = 'Error uploading file using new API: ${response.statusCode}';
        Logger.error(errorMessage);

        // Display alert with response message for non-200 status codes
        String alertMessage = errorMessage;
        if (response.data != null &&
            response.data is Map &&
            response.data.containsKey('message')) {
          alertMessage = response.data['message'];
        }

        return {
          'success': false,
          'message': alertMessage,
        };
      }
    } catch (e) {
      Logger.error('Exception uploading file using new API: $e');

      // Handle specific error types for better user experience
      String errorMessage = 'An error occurred during file upload';

      if (e.toString().contains('DioException')) {
        if (e.toString().contains('SocketException')) {
          errorMessage =
              'Network error. Please check your internet connection.';
        } else if (e.toString().contains('Connection refused')) {
          errorMessage =
              'File upload service is unreachable. Please try again later.';
        } else if (e.toString().contains('404')) {
          errorMessage =
              'File upload service not found. Please contact support.';
        } else if (e.toString().contains('timeout')) {
          errorMessage = 'Request timed out. Please try again.';
        } else if (e.toString().contains('413')) {
          errorMessage = 'File is too large. Please select a smaller file.';
        }
      }

      return {
        'success': false,
        'message': errorMessage,
      };
    }
  }

  // Method to check job status (for async processing)
  Future<Map<String, dynamic>> checkJobStatus(String jobId) async {
    try {
      final response = await _dio.get('$_newApiUrl/status/$jobId');
      
      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data,
        };
      } else {
        return {
          'success': false,
          'message': 'Failed to check job status: ${response.statusCode}',
        };
      }
    } catch (e) {
      Logger.error('Exception checking job status: $e');
      return {
        'success': false,
        'message': 'Error checking job status: $e',
      };
    }
  }
}