import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'app_config.dart';
import '../utils/logger.dart';

/// A class that provides access to environment variables and API endpoints
class Environment {
  // Private constructor to prevent instantiation
  Environment._();

  // Base URLs
  static String get transactionApiBaseUrl => AppConfig.transactionBaseUrl;
  static String get buildApiBaseUrl => AppConfig.buildBaseUrl;
  static String get chatApiBaseUrl => AppConfig.chatBaseUrl;
  static String get authApiBaseUrl => AppConfig.authBaseUrl;
  static String get workflowApiBaseUrl => AppConfig.workflowBaseUrl;
  static String get adaptersBaseUrl => AppConfig.adaptersBaseUrl;
  static String get validateBaseUrl => AppConfig.validateBaseUrl;
  static String get brdBaseUrl => AppConfig.brdBaseUrl;
  static String get conversationUrl => AppConfig.conversationApiUrl;
  static String get nslBaseUrl => AppConfig.nslBaseUrl;
  static String get javaFileApiBaseUrl => AppConfig.javaFileApiBaseUrl;
  static String get audioApiBaseUrl => AppConfig.audioApiBaseUrl;
  static String get discoveryApiBaseUrl => AppConfig.discoveryApiBaseUrl;
  static String get fileUploadApiBaseUrl => AppConfig.fileUploadApiBaseUrl;
  static String get nslHierarchyApiBaseUrl => AppConfig.nslHierarchyApiBaseUrl;
  static String get objectCreationApiBaseUrl => AppConfig.objectCreationApiBaseUrl;
  static String get ocrApiBaseUrl => AppConfig.ocrApiBaseUrl;

  // Transaction API Endpoints
  static String get transactionsUrl =>
      '$transactionApiBaseUrl/api/transactions';
  static String get executeUrl => '$transactionApiBaseUrl/api/execute';
  static String get globalObjectivesUrl =>
      '$transactionApiBaseUrl/api/v1/global-objectives/';
  static String get transactionDetailsUrl =>
      '$transactionApiBaseUrl/api/v1/global-objectives/transactionsdetails';

  // Workflow API Endpoints
  static String get workflowInstancesUrl =>
      '$workflowApiBaseUrl/api/v1/workflows/instances';
  static String workflowInstanceUrl(String instanceId) =>
      '$workflowInstancesUrl/$instanceId';
  static String workflowStartUrl(String instanceId) =>
      '${workflowInstanceUrl(instanceId)}/start';
  static String workflowExecuteUrl(String instanceId) =>
      '${workflowInstanceUrl(instanceId)}/execute';
  static String workflowInputsUrl(String instanceId) =>
      '${workflowInstanceUrl(instanceId)}/inputs';

  // Build API Endpoints
  static String get buildChatUrl => '$buildApiBaseUrl/api/chat';
  static String get solutionsUrl => '$buildApiBaseUrl/api/solutions';
  static String get deployUrl => '$buildApiBaseUrl/api/deploy_by_id';

  // Chat API Endpoints
  static String get chatCompletionsUrl => '$chatApiBaseUrl/chat/completions';

  // Auth API Endpoints
  // static String get loginUrl => '$authApiBaseUrl/api/v1/auth/auth/token';
  static String get loginUrl => '$authApiBaseUrl/api/v2/auth/login';

  // static String get registerUrl => '$authApiBaseUrl/api/v1/auth/auth/register';
  static String get registerUrl => '$authApiBaseUrl/api/v2/auth/register';
  // static String get logoutUrl => '$authApiBaseUrl/api/v1/auth/auth/logout';
  static String get logoutUrl => '$authApiBaseUrl/api/v2/auth/logout';
  // static String get refreshTokenUrl =>
  //     '$authApiBaseUrl/api/v1/auth/auth/refresh';
  static String get refreshTokenUrl => '$authApiBaseUrl/api/v2/auth/refresh';
  // static String get userProfileUrl => '$authApiBaseUrl/api/v1/auth/auth/me';
  static String get userProfileUrl => '$authApiBaseUrl/api/v2/auth/profile/';

  // Java File API Endpoints
  static String get javaFileReadUrl => '$javaFileApiBaseUrl/api/java-files/read';

  // Audio API Endpoints
  static String get audioUploadUrl => '$audioApiBaseUrl/api/upload';
  static String get speechToTextUrl => '$audioApiBaseUrl/api/speect_to_text';

  // Discovery API Endpoints
  static String get discoveryChatUrl => '$discoveryApiBaseUrl/chat';

  // NSL RAG System API Endpoints
  static String get nslConversationsUrl => '$nslBaseUrl/api/v1/conversations';
  static String get nslMessagesUrl => '$nslBaseUrl/api/v1/messages';

  // BRD API Endpoints
  static String get brdProjectsUrl => '$brdBaseUrl/api/v1/projects';
  static String get brdConversationsUrl => '$brdBaseUrl/api/v1/brd';
  static String get brdModelsUrl => '$brdBaseUrl/api/v1/models';

  // File Upload API Endpoints
  static String get fileUploadProcessUrl => '$fileUploadApiBaseUrl/api/v1/process';

  // NSL Hierarchy API Endpoints
  static String get nslHierarchyModulesUrl => '$nslHierarchyApiBaseUrl/nsl-prescriptor/api/v1/modules';

  // NSL Hub Boxing API Endpoints (uses Java File API base)
  static String get nslHubBoxingUrl => '$javaFileApiBaseUrl/api/nslhubboxing';

  // NSL Hub Java Box Grouped API Endpoints (uses Java File API base)
  static String get nslHubJavaBoxGroupedUrl => '$javaFileApiBaseUrl/api/nslhub-java-box/grouped';

  // NSL Java Solution API Endpoints (uses Java File API base)
  static String get nslJavaSolutionActiveUrl => '$javaFileApiBaseUrl/api/global-objectives/active';

  // Object Creation API Endpoints
  static String get objectCreationBaseUrl => '$objectCreationApiBaseUrl/api/v1';

  // OCR API Endpoints
  static String get ocrProcessUrl => '$ocrApiBaseUrl/api/v1';

  // API Keys
  static String? get openAiApiKey => dotenv.env['API_KEY'];

  /// Initialize the environment
  static Future<void> init() async {
    try {
      Logger.info('Initializing Environment');

      // Load environment variables
      await AppConfig.init();

      Logger.info('Environment initialized successfully');
    } catch (e) {
      Logger.error('Error initializing Environment: $e');
      rethrow;
    }
  }
}
