import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nsl/models/custom_image.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/providers/web_home_provider_static.dart';
import 'package:nsl/screens/web/new_design/widgets/custom_tab_header.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';
import '../utils/font_manager.dart';
import '../theme/app_colors.dart';
import 'package:nsl/l10n/app_localizations.dart';

class WebCreationFlowMainScreen extends StatefulWidget {
  const WebCreationFlowMainScreen({Key? key}) : super(key: key);

  @override
  State<WebCreationFlowMainScreen> createState() =>
      _WebCreationFlowMainScreenState();
}

class _WebCreationFlowMainScreenState extends State<WebCreationFlowMainScreen> {
  String _selectedTab = '';
  int _currentPage = 0;
  final int _itemsPerPage = 10;
  String _sortColumn = 'lastOpened';
  bool _sortAscending = false;

  // Search functionality
  final TextEditingController _searchController = TextEditingController();
  bool _showSearchBar = false;

  // Category filter functionality
  String? _selectedCategoryFilter; // For main tablet view
  String? _selectedRecentFilter; // For Recent modal
  String? _selectedFavouriteFilter; // For Favourite modal
  String? _selectedChatFilter; // For Chat modal

  // Hover state tracking for table rows
  int? _hoveredRowIndex;

  // Hover state tracking for sortable headers
  String? _hoveredHeaderColumn;

  // Scroll controller for synchronized horizontal scrolling
  final ScrollController _horizontalScrollController = ScrollController();

  final List<Map<String, dynamic>> _allData = [
    // Role entries
    {
      'fileName': 'CEO',
      'type': 'Role',
      'project': '',
      'solution': '',
      'object': '',
      'role': 'Role',
      'lastOpened': DateTime(2025, 9, 24),
      'isFavorite': true,
      'status': 'Draft',
    },
    {
      'fileName': 'Product Manager',
      'type': 'Role',
      'project': '',
      'solution': '',
      'object': '',
      'role': 'Role',
      'lastOpened': DateTime(2025, 9, 23),
      'isFavorite': false,
      'status': 'Published',
    },
    {
      'fileName': 'Admin',
      'type': 'Role',
      'project': '',
      'solution': '',
      'object': '',
      'role': 'Role',
      'lastOpened': DateTime(2025, 9, 21),
      'isFavorite': false,
      'status': 'Draft',
    },

    // Object entries
    {
      'fileName': 'Employee',
      'type': 'Object',
      'project': '',
      'solution': '',
      'object': 'Object',
      'role': '',
      'lastOpened': DateTime(2025, 9, 24),
      'isFavorite': false,
      'status': 'Published',
    },
    {
      'fileName': 'Order',
      'type': 'Object',
      'project': '',
      'solution': '',
      'object': 'Object',
      'role': '',
      'lastOpened': DateTime(2025, 9, 22),
      'isFavorite': false,
      'status': 'Draft',
    },
    {
      'fileName': 'Customer',
      'type': 'Object',
      'project': '',
      'solution': '',
      'object': 'Object',
      'role': '',
      'lastOpened': DateTime(2025, 9, 21),
      'isFavorite': true,
      'status': 'Published',
    },
    {
      'fileName': 'Product',
      'type': 'Object',
      'project': '',
      'solution': '',
      'object': 'Object',
      'role': '',
      'lastOpened': DateTime(2025, 9, 20),
      'isFavorite': true,
      'status': 'Draft',
    },

    // Solution entries
    {
      'fileName': 'Customer order product',
      'type': 'Solution',
      'project': '',
      'solution': 'Solution',
      'object': '',
      'role': '',
      'lastOpened': DateTime(2025, 9, 24),
      'isFavorite': false,
      'status': 'Published',
    },
    {
      'fileName': 'Inventory System',
      'type': 'Solution',
      'project': '',
      'solution': 'Solution',
      'object': '',
      'role': '',
      'lastOpened': DateTime(2025, 9, 23),
      'isFavorite': true,
      'status': 'Draft',
    },
    {
      'fileName': 'Sales Dashboard',
      'type': 'Solution',
      'project': '',
      'solution': 'Solution',
      'object': '',
      'role': '',
      'lastOpened': DateTime(2025, 9, 22),
      'isFavorite': false,
      'status': 'Draft',
    },
    {
      'fileName': 'Payment System',
      'type': 'Solution',
      'project': '',
      'solution': 'Solution',
      'object': '',
      'role': '',
      'lastOpened': DateTime(2025, 9, 20),
      'isFavorite': false,
      'status': 'Published',
    },

    {
      'fileName': 'E-commerce Platform',
      'type': 'Project',
      'project': 'Project',
      'solution': '',
      'object': '',
      'role': '',
      'lastOpened': DateTime(2025, 9, 25),
      'isFavorite': true,
      'status': 'Draft',
    },
    // {
    //   'project': 'Project',
    //   'solution': '',
    //   'object': '',
    //   'role': '',
    //   'lastOpened': DateTime(2025, 9, 24),
    //   'isFavorite': false,
    //   'status': 'Published',
    // },
    {
      'fileName': 'Mobile App Development',
      'type': 'Project',
      'project': 'Project',
      'solution': '',
      'object': '',
      'role': '',
      'lastOpened': DateTime(2025, 9, 23),
      'isFavorite': true,
      'status': 'Draft',
    },
    {
      'fileName': 'Data Analytics Dashboard',
      'type': 'Project',
      'project': 'Project',
      'solution': '',
      'object': '',
      'role': '',
      'lastOpened': DateTime(2025, 9, 22),
      'isFavorite': false,
      'status': 'Published',
    },
  ];
// Updated _filteredData getter to handle status filtering
  List<Map<String, dynamic>> get _filteredData {
    List<Map<String, dynamic>> filtered = [];

    // Get translated strings for comparison
    final favouriteText = AppLocalizations.of(context)!
        .translate('mylibrary.buttonText.favourite');

    if (_selectedTab == favouriteText) {
      filtered = _allData.where((item) => item['isFavorite'] == true).toList();
    } else {
      // Default to Recent
      filtered = List.from(_allData);
    }

    // Apply category filter (including status filter)
    if (_selectedCategoryFilter != null) {
      if (_selectedCategoryFilter == 'Draft' ||
          _selectedCategoryFilter == 'Published') {
        // Filter by status
        filtered = filtered
            .where((item) => item['status'] == _selectedCategoryFilter)
            .toList();
      } else {
        // Filter by type
        filtered = filtered
            .where((item) => item['type'] == _selectedCategoryFilter)
            .toList();
      }
    }

    // Apply search filter
    final searchText = _searchController.text.toLowerCase();
    if (searchText.isNotEmpty) {
      filtered = filtered
          .where((item) =>
              (item['fileName']?.toLowerCase().contains(searchText) ?? false) ||
              (item['type']?.toLowerCase().contains(searchText) ?? false))
          .toList();
    }

    // Sort the data by last opened (most recent first)
    filtered.sort((a, b) {
      DateTime aDate = a['lastOpened'];
      DateTime bDate = b['lastOpened'];
      return bDate.compareTo(aDate);
    });

    return filtered;
  }

  List<Map<String, dynamic>> get _paginatedData {
    final startIndex = _currentPage * _itemsPerPage;
    final endIndex =
        (startIndex + _itemsPerPage).clamp(0, _filteredData.length);
    return _filteredData.sublist(startIndex, endIndex);
  }

  int get _totalPages => (_filteredData.length / _itemsPerPage).ceil();

  // Calculate the minimum width needed for the table to prevent overflow
  double _calculateTableWidth() {
    // Base width calculation considering all columns
    // File Name column (flex: 3) - minimum 350px
    // Last Opened column (flex: 2) - minimum 250px
    // Favorites column (flex: 1) - minimum 100px
    // Sort With spacer (flex: 1) - minimum 150px
    // Status columns (flex: 5) - minimum 750px (6 columns * ~125px each for very long text like "Veröffentlicht")

    final screenWidth = MediaQuery.of(context).size.width;
    const minTableWidth =
        1600.0; // Further increased to accommodate very long text in all languages including German "Veröffentlicht"

    // Return the larger of minimum width or current screen width
    return screenWidth > minTableWidth ? screenWidth : minTableWidth;
  }

  void _sortTable(String column) {
    setState(() {
      if (_sortColumn == column) {
        _sortAscending = !_sortAscending;
      } else {
        _sortColumn = column;
        _sortAscending = true;
      }
      _currentPage = 0; // Reset to first page when sorting
    });
  }

  void _showDiscoverModal() {
    final screenWidth = MediaQuery.of(context).size.width;

    // For tablet devices (768px), use full-screen slide-in modal
    if (screenWidth >= 600 && screenWidth <= 1023) {
      Navigator.of(context).push(
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) {
            return _buildFullScreenDiscoverModal();
          },
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(-1.0, 0.0);
            const end = Offset.zero;
            const curve = Curves.easeInOut;

            var tween = Tween(begin: begin, end: end).chain(
              CurveTween(curve: curve),
            );

            return SlideTransition(
              position: animation.drive(tween),
              child: child,
            );
          },
          transitionDuration: const Duration(milliseconds: 300),
          reverseTransitionDuration: const Duration(milliseconds: 300),
        ),
      );
    } else {
      // For other screen sizes, use the existing modal
      showDialog(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext context) {
          return _buildModal(
            title: 'How Discover Works - Your AI-Guided Journey',
            content: _buildDiscoverModalContent(),
          );
        },
      );
    }
  }

  void _showDevelopModal() {
    final screenWidth = MediaQuery.of(context).size.width;

    // For tablet devices (768px), use full-screen slide-in modal
    if (screenWidth >= 600 && screenWidth <= 1023) {
      Navigator.of(context).push(
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) {
            return _buildFullScreenDevelopModal();
          },
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(-1.0, 0.0);
            const end = Offset.zero;
            const curve = Curves.easeInOut;

            var tween = Tween(begin: begin, end: end).chain(
              CurveTween(curve: curve),
            );

            return SlideTransition(
              position: animation.drive(tween),
              child: child,
            );
          },
          transitionDuration: const Duration(milliseconds: 300),
          reverseTransitionDuration: const Duration(milliseconds: 300),
        ),
      );
    } else {
      // For other screen sizes, use the existing modal
      showDialog(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext context) {
          return _buildModal(
            title: 'How Development Works - Your Custom Build Journey',
            content: _buildDevelopModalContent(),
          );
        },
      );
    }
  }

  void _showRecentModal() {
    final screenWidth = MediaQuery.of(context).size.width;

    // For tablet devices (768px), use full-screen slide-in modal from right
    if (screenWidth >= 600 && screenWidth <= 1023) {
      Navigator.of(context).push(
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) {
            return _buildFullScreenRecentModal();
          },
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(1.0, 0.0); // Slide in from right
            const end = Offset.zero;
            const curve = Curves.easeInOut;

            var tween = Tween(begin: begin, end: end).chain(
              CurveTween(curve: curve),
            );

            return SlideTransition(
              position: animation.drive(tween),
              child: child,
            );
          },
          transitionDuration: const Duration(milliseconds: 300),
          reverseTransitionDuration: const Duration(milliseconds: 300),
        ),
      );
    }
  }

  void _showFavouriteModal() {
    final screenWidth = MediaQuery.of(context).size.width;

    // For tablet devices (768px), use full-screen slide-in modal from right
    if (screenWidth >= 600 && screenWidth <= 1023) {
      Navigator.of(context).push(
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) {
            return _buildFullScreenFavouriteModal();
          },
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(1.0, 0.0); // Slide in from right
            const end = Offset.zero;
            const curve = Curves.easeInOut;

            var tween = Tween(begin: begin, end: end).chain(
              CurveTween(curve: curve),
            );

            return SlideTransition(
              position: animation.drive(tween),
              child: child,
            );
          },
          transitionDuration: const Duration(milliseconds: 300),
          reverseTransitionDuration: const Duration(milliseconds: 300),
        ),
      );
    }
  }

  void _showChatModal() {
    final screenWidth = MediaQuery.of(context).size.width;

    // For tablet devices (768px), use full-screen slide-in modal from bottom
    if (screenWidth >= 600 && screenWidth <= 1023) {
      Navigator.of(context).push(
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) {
            return _buildFullScreenChatModal();
          },
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(0.0, 1.0); // Slide in from bottom
            const end = Offset.zero;
            const curve = Curves.easeInOut;

            var tween = Tween(begin: begin, end: end).chain(
              CurveTween(curve: curve),
            );

            return SlideTransition(
              position: animation.drive(tween),
              child: child,
            );
          },
          transitionDuration: const Duration(milliseconds: 300),
          reverseTransitionDuration: const Duration(milliseconds: 300),
        ),
      );
    }
  }

  void _showFilterModal() {
    final screenWidth = MediaQuery.of(context).size.width;

    // For tablet devices (768px), use bottom sheet modal to preserve background
    if (screenWidth >= 600 && screenWidth <= 1023) {
      showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        barrierColor: Colors.black.withOpacity(0.4), // Semi-transparent overlay
        isScrollControlled: true,
        enableDrag: false,
        isDismissible: true,
        constraints: BoxConstraints.expand(),
        builder: (BuildContext context) {
          return Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              width: double.infinity,
              margin: EdgeInsets.zero,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(24),
                  topRight: Radius.circular(24),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.15),
                    blurRadius: 20,
                    offset: Offset(0, -4),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Header with title and close button
                  Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 16),
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: Color(0xFFE5E5E5),
                          width: 1,
                        ),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Sort by',
                          style: TextStyle(
                            fontFamily: FontManager.fontFamilyTiemposText,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF000000),
                          ),
                        ),
                        MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: () => Navigator.of(context).pop(),
                            child: Icon(
                              Icons.close,
                              size: 22,
                              color: Colors.black,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Filter options content - No checkboxes, just icons and text
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        _buildSimpleFilterOption(
                          icon: 'assets/images/my_library/create_project.svg',
                          label: 'Projects',
                          onTap: () {
                            _filterByCategory('project');
                            Navigator.of(context).pop();
                          },
                        ),
                        _buildSimpleFilterOption(
                          icon: 'assets/images/my_library/create_solution.svg',
                          label: 'Solutions',
                          onTap: () {
                            _filterByCategory('solution');
                            Navigator.of(context).pop();
                          },
                        ),
                        _buildSimpleFilterOption(
                          icon: 'assets/images/my_library/create_object.svg',
                          label: 'Objects',
                          onTap: () {
                            _filterByCategory('object');
                            Navigator.of(context).pop();
                          },
                        ),
                        _buildSimpleFilterOption(
                          icon: 'assets/images/my_library/create_role.svg',
                          label: 'Roles/Agent',
                          onTap: () {
                            _filterByCategory('role');
                            Navigator.of(context).pop();
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      );
    }
  }

  void _showRecentFilterModal() {
    final screenWidth = MediaQuery.of(context).size.width;

    // For tablet devices (768px), use bottom sheet modal to preserve background
    if (screenWidth >= 600 && screenWidth <= 1023) {
      showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        barrierColor: Colors.black.withOpacity(0.4), // Semi-transparent overlay
        isScrollControlled: true,
        enableDrag: false,
        isDismissible: true,
        constraints: BoxConstraints.expand(),
        builder: (BuildContext context) {
          return Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              width: double.infinity,
              margin: EdgeInsets.zero,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(24),
                  topRight: Radius.circular(24),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.15),
                    blurRadius: 20,
                    offset: Offset(0, -4),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Header with title and close button
                  Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 16),
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: Color(0xFFE5E5E5),
                          width: 1,
                        ),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Sort by',
                          style: TextStyle(
                            fontFamily: FontManager.fontFamilyTiemposText,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF000000),
                          ),
                        ),
                        MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: () => Navigator.of(context).pop(),
                            child: Icon(
                              Icons.close,
                              size: 22,
                              color: Colors.black,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Filter options content - No checkboxes, just icons and text
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        _buildSimpleFilterOption(
                          icon: 'assets/images/my_library/create_project.svg',
                          label: 'Projects',
                          onTap: () {
                            _filterRecentByCategory('project');
                            Navigator.of(context).pop();
                          },
                        ),
                        _buildSimpleFilterOption(
                          icon: 'assets/images/my_library/create_solution.svg',
                          label: 'Solutions',
                          onTap: () {
                            _filterRecentByCategory('solution');
                            Navigator.of(context).pop();
                          },
                        ),
                        _buildSimpleFilterOption(
                          icon: 'assets/images/my_library/create_object.svg',
                          label: 'Objects',
                          onTap: () {
                            _filterRecentByCategory('object');
                            Navigator.of(context).pop();
                          },
                        ),
                        _buildSimpleFilterOption(
                          icon: 'assets/images/my_library/create_role.svg',
                          label: 'Roles/Agent',
                          onTap: () {
                            _filterRecentByCategory('role');
                            Navigator.of(context).pop();
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      );
    }
  }

  // Separate filter function for Recent modal with callback
  void _filterRecentByCategoryWithCallback(
      String column, Function setModalState) {
    setState(() {
      if (column == 'draft' || column == 'published') {
        // Handle status filtering
        final status = column == 'draft' ? 'Draft' : 'Published';
        if (_selectedRecentFilter == status) {
          _selectedRecentFilter = null;
        } else {
          _selectedRecentFilter = status;
        }
      } else {
        // Handle type filtering
        final category = _getCategoryFromColumn(column);
        if (_selectedRecentFilter == category) {
          _selectedRecentFilter = null;
        } else {
          _selectedRecentFilter = category;
        }
      }
    });
    // Also update the modal state
    setModalState(() {});
  }

  // Recent filter modal that accepts a callback to refresh modal
  void _showRecentFilterModalWithCallback(Function setModalState) {
    final screenWidth = MediaQuery.of(context).size.width;

    // For tablet devices (768px), use bottom sheet modal to preserve background
    if (screenWidth >= 600 && screenWidth <= 1023) {
      showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        barrierColor: Colors.black.withOpacity(0.4), // Semi-transparent overlay
        isScrollControlled: true,
        enableDrag: false,
        isDismissible: true,
        constraints: BoxConstraints.expand(),
        builder: (BuildContext context) {
          return Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              width: double.infinity,
              margin: EdgeInsets.zero,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(24),
                  topRight: Radius.circular(24),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.15),
                    blurRadius: 20,
                    offset: Offset(0, -4),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Header with title and close button
                  Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 16),
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: Color(0xFFE5E5E5),
                          width: 1,
                        ),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Sort by',
                          style: TextStyle(
                            fontFamily: FontManager.fontFamilyTiemposText,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF000000),
                          ),
                        ),
                        MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: () => Navigator.of(context).pop(),
                            child: Icon(
                              Icons.close,
                              size: 22,
                              color: Colors.black,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Filter options content - No checkboxes, just icons and text
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        _buildSimpleFilterOption(
                          icon: 'assets/images/my_library/create_project.svg',
                          label: 'Projects',
                          onTap: () {
                            _filterRecentByCategoryWithCallback(
                                'project', setModalState);
                            Navigator.of(context).pop();
                          },
                        ),
                        _buildSimpleFilterOption(
                          icon: 'assets/images/my_library/create_solution.svg',
                          label: 'Solutions',
                          onTap: () {
                            _filterRecentByCategoryWithCallback(
                                'solution', setModalState);
                            Navigator.of(context).pop();
                          },
                        ),
                        _buildSimpleFilterOption(
                          icon: 'assets/images/my_library/create_object.svg',
                          label: 'Objects',
                          onTap: () {
                            _filterRecentByCategoryWithCallback(
                                'object', setModalState);
                            Navigator.of(context).pop();
                          },
                        ),
                        _buildSimpleFilterOption(
                          icon: 'assets/images/my_library/create_role.svg',
                          label: 'Roles/Agent',
                          onTap: () {
                            _filterRecentByCategoryWithCallback(
                                'role', setModalState);
                            Navigator.of(context).pop();
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      );
    }
  }

// Favourite filter modal that accepts a callback to refresh modal
  void _showFavouriteFilterModalWithCallback(Function setModalState) {
    final screenWidth = MediaQuery.of(context).size.width;

    // For tablet devices (768px), use bottom sheet modal to preserve background
    if (screenWidth >= 600 && screenWidth <= 1023) {
      showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        barrierColor: Colors.black.withOpacity(0.4), // Semi-transparent overlay
        isScrollControlled: true,
        enableDrag: false,
        isDismissible: true,
        constraints: BoxConstraints.expand(),
        builder: (BuildContext context) {
          return Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              width: double.infinity,
              margin: EdgeInsets.zero,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(24),
                  topRight: Radius.circular(24),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.15),
                    blurRadius: 20,
                    offset: Offset(0, -4),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Header with title and close button
                  Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 16),
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: Color(0xFFE5E5E5),
                          width: 1,
                        ),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Sort by',
                          style: TextStyle(
                            fontFamily: FontManager.fontFamilyTiemposText,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF000000),
                          ),
                        ),
                        MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: () => Navigator.of(context).pop(),
                            child: Icon(
                              Icons.close,
                              size: 22,
                              color: Colors.black,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Filter options content - No checkboxes, just icons and text
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        _buildSimpleFilterOption(
                          icon: 'assets/images/my_library/create_project.svg',
                          label: 'Projects',
                          onTap: () {
                            _filterFavouriteByCategoryWithCallback(
                                'project', setModalState);
                            Navigator.of(context).pop();
                          },
                        ),
                        _buildSimpleFilterOption(
                          icon: 'assets/images/my_library/create_solution.svg',
                          label: 'Solutions',
                          onTap: () {
                            _filterFavouriteByCategoryWithCallback(
                                'solution', setModalState);
                            Navigator.of(context).pop();
                          },
                        ),
                        _buildSimpleFilterOption(
                          icon: 'assets/images/my_library/create_object.svg',
                          label: 'Objects',
                          onTap: () {
                            _filterFavouriteByCategoryWithCallback(
                                'object', setModalState);
                            Navigator.of(context).pop();
                          },
                        ),
                        _buildSimpleFilterOption(
                          icon: 'assets/images/my_library/create_role.svg',
                          label: 'Roles/Agent',
                          onTap: () {
                            _filterFavouriteByCategoryWithCallback(
                                'role', setModalState);
                            Navigator.of(context).pop();
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      );
    }
  }

  // Separate filter function for Favourite modal with callback
  void _filterFavouriteByCategoryWithCallback(
      String column, Function setModalState) {
    setState(() {
      if (column == 'draft' || column == 'published') {
        // Handle status filtering
        final status = column == 'draft' ? 'Draft' : 'Published';
        if (_selectedFavouriteFilter == status) {
          _selectedFavouriteFilter = null;
        } else {
          _selectedFavouriteFilter = status;
        }
      } else {
        // Handle type filtering
        final category = _getCategoryFromColumn(column);
        if (_selectedFavouriteFilter == category) {
          _selectedFavouriteFilter = null;
        } else {
          _selectedFavouriteFilter = category;
        }
      }
    });
    // Also update the modal state
    setModalState(() {});
  }

  // Separate filter function for Recent modal
  void _filterRecentByCategory(String column) {
    setState(() {
      if (column == 'draft' || column == 'published') {
        // Handle status filtering
        final status = column == 'draft' ? 'Draft' : 'Published';
        if (_selectedRecentFilter == status) {
          _selectedRecentFilter = null;
        } else {
          _selectedRecentFilter = status;
        }
      } else {
        // Handle type filtering
        final category = _getCategoryFromColumn(column);
        if (_selectedRecentFilter == category) {
          _selectedRecentFilter = null;
        } else {
          _selectedRecentFilter = category;
        }
      }
    });
  }

  // Separate filter function for Favourite modal
  void _filterFavouriteByCategory(String column) {
    setState(() {
      if (column == 'draft' || column == 'published') {
        // Handle status filtering
        final status = column == 'draft' ? 'Draft' : 'Published';
        if (_selectedFavouriteFilter == status) {
          _selectedFavouriteFilter = null;
        } else {
          _selectedFavouriteFilter = status;
        }
      } else {
        // Handle type filtering
        final category = _getCategoryFromColumn(column);
        if (_selectedFavouriteFilter == category) {
          _selectedFavouriteFilter = null;
        } else {
          _selectedFavouriteFilter = category;
        }
      }
    });
  }

  // Separate filter function for Chat modal
  void _filterChatByCategory(String column) {
    setState(() {
      if (column == 'draft' || column == 'published') {
        // Handle status filtering
        final status = column == 'draft' ? 'Draft' : 'Published';
        if (_selectedChatFilter == status) {
          _selectedChatFilter = null;
        } else {
          _selectedChatFilter = status;
        }
      } else {
        // Handle type filtering
        final category = _getCategoryFromColumn(column);
        if (_selectedChatFilter == category) {
          _selectedChatFilter = null;
        } else {
          _selectedChatFilter = category;
        }
      }
    });
  }

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_filterData);
  }

  @override
  void dispose() {
    _searchController.removeListener(_filterData);
    _searchController.dispose();
    super.dispose();
  }

  // Filter data based on search text
  void _filterData() {
    setState(() {
      _currentPage = 0; // Reset to first page when filtering
    });
  }

  // Toggle search bar visibility
  void _toggleSearchBar() {
    setState(() {
      _showSearchBar = !_showSearchBar;
      if (!_showSearchBar) {
        // Clear search when hiding search bar
        _searchController.clear();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final isTabletVersion = MediaQuery.of(context).size.width >= 600 &&
        MediaQuery.of(context).size.width <= 1023;
    return Scaffold(
      backgroundColor: Color(0xffF7F9FB),
      body: Row(
        children: [
          Expanded(
            child: SizedBox(),
          ),
          Expanded(
            flex: 10,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Fixed header
                CustomTabHeader(),

                // Scrollable content
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(AppSpacing.sm),
                      child: Column(
                        children: [
                          _buildMainContent(),
                          const SizedBox(height: 20),
                          // Bottom section with constrained height
                          SizedBox(
                            height: MediaQuery.of(context).size.height * 0.88,
                            child: _buildBottomSection(),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: SizedBox(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const SizedBox(), // Left placeholder

        // Center-aligned tabs
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildCenterTabItem(
                icon: Icons.library_books, label: 'My Library', isActive: true),
            const SizedBox(width: 16),
            _buildCenterTabItem(icon: Icons.event_note, label: 'Organizer'),
            const SizedBox(width: 16),
            _buildCenterTabItem(icon: Icons.bug_report, label: 'Testing'),
          ],
        ),

        // Right-aligned "More templates"
        Text(
          'More templates',
          style: TextStyle(
            fontFamily: FontManager.fontFamilyTiemposText,
            fontSize: ResponsiveFontSizes.titleLarge(context),
            fontWeight: FontWeight.w500,
            color: AppColors.textBlue, // Text color
            decoration: TextDecoration.underline, // Underline enabled
            decorationColor: AppColors.textBlue, // Underline color set to blue
          ),
        ),
      ],
    );
  }

  Widget _buildCenterTabItem(
      {required IconData icon, required String label, bool isActive = false}) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: isActive ? AppColors.primaryBlue : AppColors.textGreyColor,
        ),
        const SizedBox(width: 6),
        Text(
          label,
          style: TextStyle(
            fontFamily: FontManager.fontFamilyTiemposText,
            fontSize: ResponsiveFontSizes.titleLarge(context),
            fontWeight: FontWeight.w500,
            color: isActive ? AppColors.primaryBlue : AppColors.textGreyColor,
          ),
        ),
      ],
    );
  }

  Widget _buildMainContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // More templates at the top
        Align(
          alignment: Alignment.topRight,
          child: MouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: () {
                // Handle more templates tap
              },
              child: Text(
                'More templates',
                style: TextStyle(
                  fontFamily: FontManager.fontFamilyTiemposText,
                  fontSize: ResponsiveFontSizes.titleLarge(context),
                  fontWeight: FontWeight.w500,
                  color: AppColors.textBlue,
                  decoration: TextDecoration.underline,
                  decorationColor: AppColors.textBlue,
                ),
              ),
            ),
          ),
        ),
        const SizedBox(height: 16),
        // Main content sections
        LayoutBuilder(
          builder: (context, constraints) {
            final screenWidth = MediaQuery.of(context).size.width;

            // Check if screen width is less than 600px (mobile portrait mode)
            if (screenWidth < 600) {
              // Mobile portrait mode: Full width sections stacked vertically
              return Column(
                children: [
                  _buildDiscoverSection(),
                  const SizedBox(height: 24),
                  _buildDevelopSection(),
                ],
              );
            } else if (screenWidth >= 600 && screenWidth <= 1023) {
              // Tablet mode: Side by side sections with same design and spacing
              return IntrinsicHeight(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Expanded(
                      child: _buildDiscoverSection(),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildDevelopSection(),
                    ),
                  ],
                ),
              );
            } else {
              // Desktop mode: Full width sections stacked vertically
              return Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    width: MediaQuery.of(context).size.width / 4.7,
                    child: _buildDiscoverSection(),
                  ),
                  const SizedBox(width: 24),
                  SizedBox(
                    width: MediaQuery.of(context).size.width / 4.7,
                    child: _buildDevelopSection(),
                  ),
                ],
              );
            }
          },
        ),
      ],
    );
  }

  Widget _buildDiscoverSection() {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () {
          // Clear chat data for fresh start when navigating from Discover section
          final webHomeProviderStatic =
              Provider.of<WebHomeProviderStatic>(context, listen: false);
          final webHomeProvider =
              Provider.of<WebHomeProvider>(context, listen: false);

          // Reset both providers to ensure complete data clearing
          webHomeProviderStatic.resetConversation(preserveScreenIndex: true);
          webHomeProvider.resetConversation(preserveScreenIndex: true);

          // Clear any additional chat-related data
          webHomeProviderStatic.clearMessages();
          webHomeProvider.clearMessages();

          // Clear chat controllers
          webHomeProviderStatic.chatController.clear();
          webHomeProvider.chatController.clear();

          // Reset UI state completely
          webHomeProviderStatic.resetUIState(preserveScreenIndex: true);
          webHomeProvider.resetUIState(preserveScreenIndex: true);

          // Clear any NSL session data
          webHomeProvider.resetNslSession();
          webHomeProvider.resetModeSession();

          // Clear any solution session data
          webHomeProviderStatic.solutionSessionModel = null;
          webHomeProvider.solutionSessionModel = null;

          // Force clear any cached data
          webHomeProviderStatic.lastUserMessageForApi = '';
          webHomeProvider.lastUserMessageForApi = '';

          // Navigate to the screen
          webHomeProvider.currentScreenIndex = ScreenConstants.create;
          webHomeProviderStatic.currentCreateScreenIndex = 1;
        },
        child: Container(
          padding: const EdgeInsets.all(AppSpacing.lg),
          decoration: BoxDecoration(
            color: Colors.white,
            // border: Border.all(color: AppColors.greyBorder),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  SvgPicture.asset(
                    'assets/images/my_library/my_library_discover.svg', // your SVG asset path
                    width: 20,
                    height: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    AppLocalizations.of(context)
                        .translate('mylibrary.cardText.discover.title'),
                    style: TextStyle(
                        fontFamily: FontManager.fontFamilyTiemposText,
                        fontSize: 16,
                        fontWeight: FontWeight.w400,
                        color: Color(0xFF242424)),
                  ),
                  const Spacer(),
                  MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                      onTap: _showDiscoverModal,
                      child: Container(
                        width: 16,
                        height: 16,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: AppColors.textGreyColor,
                            width: 1,
                          ),
                        ),
                        alignment: Alignment.center,
                        child: Text(
                          'i',
                          textAlign: TextAlign.center,
                          textHeightBehavior: const TextHeightBehavior(
                            applyHeightToFirstAscent: false,
                            applyHeightToLastDescent: false,
                          ),
                          style: TextStyle(
                            fontFamily: FontManager.fontFamilyTiemposText,
                            fontSize: 10,
                            height: 1,
                            fontWeight: FontWeight.w500,
                            color: AppColors.textGreyColor,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                AppLocalizations.of(context)
                    .translate('mylibrary.cardText.discover.description'),
                style: TextStyle(
                  fontFamily: FontManager.fontFamilyTiemposText,
                  fontSize: MediaQuery.of(context).size.width >= 600 &&
                          MediaQuery.of(context).size.width <= 1023
                      ? ResponsiveFontSizes.titleLarge(context) // Tablet size
                      : ResponsiveFontSizes.titleSmall(context), // Other sizes
                  fontWeight: FontWeight.w400,
                  color: Color(0xFF242424),
                  // height: 2,
                ),
                maxLines: 3, // 👈 Limit to 3 lines
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDevelopSection() {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          print('Develop section tapped'); // Add this for debugging

          // Direct navigation without callbacks
          try {
            Provider.of<WebHomeProviderStatic>(context, listen: false)
                .messages
                .clear();
            Provider.of<WebHomeProvider>(context, listen: false)
                .currentScreenIndex = ScreenConstants.createObjectScreenStatic;
          } catch (e) {
            print('Navigation error: $e');
          }
        },
        // hoverColor: Colors.grey.withOpacity(0.1),
        child: Container(
          padding: const EdgeInsets.all(AppSpacing.lg),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(6),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  SvgPicture.asset(
                    'assets/images/my_library/my_library_develop.svg',
                    width: 20,
                    height: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    AppLocalizations.of(context)
                        .translate('mylibrary.cardText.develop.title'),
                    style: TextStyle(
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                      color: Color(0xFF242424),
                    ),
                  ),
                  const Spacer(),
                  MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                      onTap: _showDevelopModal,
                      child: Container(
                        width: 16,
                        height: 16,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: AppColors.textGreyColor,
                            width: 1,
                          ),
                        ),
                        alignment: Alignment.center,
                        child: Text(
                          'i',
                          textAlign: TextAlign.center,
                          textHeightBehavior: const TextHeightBehavior(
                            applyHeightToFirstAscent: false,
                            applyHeightToLastDescent: false,
                          ),
                          style: TextStyle(
                            fontFamily: FontManager.fontFamilyTiemposText,
                            fontSize: 10,
                            height: 1, // ✅ Prevent extra vertical space
                            fontWeight: FontWeight.w500,
                            color: AppColors.textGreyColor,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                AppLocalizations.of(context)
                    .translate('mylibrary.cardText.develop.description'),
                style: TextStyle(
                  fontFamily: FontManager.fontFamilyTiemposText,
                  fontSize: MediaQuery.of(context).size.width >= 600 &&
                          MediaQuery.of(context).size.width <= 1023
                      ? ResponsiveFontSizes.titleLarge(context) // Tablet size
                      : ResponsiveFontSizes.titleSmall(context), // Other sizes
                  fontWeight: FontWeight.w400,
                  color: Color(0xFF242424),
                ),
                maxLines: 3, // 👈 Limit to 3 lines
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Widget _buildDevelopSection() {
  //   return GestureDetector(
  //     onTap: () {
  //     print('Develop section tapped'); // Add this for debugging
  //     // Use Future.microtask instead of addPostFrameCallback
  //     Future.microtask(() {
  //       if (mounted) {
  //         Provider.of<WebHomeProvider>(context, listen: false)
  //             .currentScreenIndex = ScreenConstants.createObjectScreenStatic;
  //       }
  //     });
  //   },
  //     child: MouseRegion(
  //        cursor: SystemMouseCursors.click,
  //       child: Container(
  //         padding: const EdgeInsets.all(AppSpacing.lg),
  //         decoration: BoxDecoration(
  //           color: Colors.white,
  //           // border: Border.all(color: AppColors.greyBorder),
  //           borderRadius: BorderRadius.circular(AppSpacing.xxs),
  //         ),
  //         child: Column(
  //           crossAxisAlignment: CrossAxisAlignment.start,
  //           children: [
  //             Row(
  //               children: [
  //                 SvgPicture.asset(
  //                   'assets/images/my_library/my_library_develop.svg', // your SVG asset path
  //                   width: 20,
  //                   height: 20,
  //                 ),
  //                 const SizedBox(width: 8),
  //                 Text(
  //                   'Develop',
  //                   style: TextStyle(
  //                       fontFamily: FontManager.fontFamilyTiemposText,
  //                       fontSize: 16,
  //                       fontWeight: FontWeight.w400,
  //                       color: Colors.black),
  //                 ),
  //                 const Spacer(),
  //                 MouseRegion(
  //                   cursor: SystemMouseCursors.click,
  //                   child: GestureDetector(
  //                     onTap: _showDevelopModal,
  //                     child: Container(
  //                       width: 16,
  //                       height: 16,
  //                       decoration: BoxDecoration(
  //                         shape: BoxShape.circle,
  //                         border:
  //                             Border.all(color: AppColors.textGreyColor, width: 1),
  //                       ),
  //                       child: Center(
  //                         child: Text(
  //                           'i',
  //                           style: TextStyle(
  //                             fontFamily: FontManager.fontFamilyTiemposText,
  //                             fontSize: 10,
  //                             fontWeight: FontWeight.w500,
  //                             color: AppColors.textGreyColor,
  //                           ),
  //                         ),
  //                       ),
  //                     ),
  //                   ),
  //                 ),
  //               ],
  //             ),
  //             const SizedBox(height: 12),
  //             Text(
  //               'Enter or upload your requirement, and we\'ll extract, develop, and refine your solution with AI-guided suggestions throughout.',
  //               style: TextStyle(
  //                 fontFamily: FontManager.fontFamilyTiemposText,
  //                 fontSize: 10,
  //                 fontWeight: FontWeight.w400,
  //                 color: AppColors.textGreyColor,
  //                 // height: 2,
  //               ),
  //             ),
  //           ],
  //         ),
  //       ),
  //     ),
  //   );
  // }

  Widget _buildBottomSection() {
    final screenWidth = MediaQuery.of(context).size.width;

    // For tablet devices (768px), use new card-based design
    if (screenWidth >= 600 && screenWidth <= 1023) {
      return _buildTabletLayout();
    }

    // For other screen sizes, use existing table layout
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTabBar(),
        const SizedBox(height: AppSpacing.xs),
        Expanded(
          child: _buildTable(),
        ),
        const SizedBox(height: AppSpacing.xs),
        _buildPagination(),
      ],
    );
  }

  // New tablet-specific layout
  Widget _buildTabletLayout() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTabletHeader(),
        const SizedBox(height: AppSpacing.xs),
        Expanded(
          child: _buildTabletCardList(),
        ),
      ],
    );
  }

  Widget _buildTabletHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 10),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Color(0xFFE6EAEE),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Recent tab - now clickable to show popup
          MouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: _showRecentModal,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                height: 42,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(
                    color: Color(0xFFE8E8E8),
                    width: 0.5,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.access_time_outlined,
                      size: 18,
                      color: AppColors.black,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      AppLocalizations.of(context)!
                          .translate('mylibrary.buttonText.recent'),
                      style: TextStyle(
                        fontFamily: FontManager.fontFamilyTiemposText,
                        fontSize: ResponsiveFontSizes.titleLarge(context),
                        fontWeight: FontWeight.w400,
                        color: Color(0xFF242424),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(width: 4),

          // Favourite tab - now clickable to show popup
          MouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: _showFavouriteModal,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                height: 42,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(
                    color: Color(0xFFE8E8E8),
                    width: 0.5,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.star_border,
                      size: 18,
                      color: AppColors.black,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      AppLocalizations.of(context)!
                          .translate('mylibrary.buttonText.favourite'),
                      style: TextStyle(
                          fontFamily: FontManager.fontFamilyTiemposText,
                          fontSize: ResponsiveFontSizes.titleLarge(context),
                          fontWeight: FontWeight.w400,
                          color: Color(0xFF242424)),
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(width: 4),

          // Chat tab - now clickable to show popup from bottom
          MouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: _showChatModal,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                height: 42,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(
                    color: Color(0xFFE8E8E8),
                    width: 0.5,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.chat_bubble_outline,
                      size: 18,
                      color: AppColors.black,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Chat',
                      style: TextStyle(
                          fontFamily: FontManager.fontFamilyTiemposText,
                          fontSize: ResponsiveFontSizes.titleLarge(context),
                          fontWeight: FontWeight.w400,
                          color: Color(0xFF242424)),
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(width: 4),

          // Sort arrows icon - design only
          MouseRegion(
              cursor: SystemMouseCursors.click,
              child: GestureDetector(
                onTap: _showFilterModal,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  height: 42,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(
                      color: const Color(0xFFE8E8E8),
                      width: .5,
                    ),
                  ),
                  child: const Center(
                    child: Icon(
                      Icons.swap_vert,
                      size: 22,
                      color: Colors.black, // Equivalent to a text grey
                    ),
                  ),
                ),
              )),

          const Spacer(),

          // Search icon - design only - Tablet code put as mention comment
          Container(
            width: 100,
            height: 42,
            padding: const EdgeInsets.symmetric(
              horizontal: 8,
            ),
            decoration: BoxDecoration(
              color: const Color(
                  0xFFF5F5F5), // Light gray background like in image
              borderRadius: BorderRadius.circular(4),
              border: Border.all(
                color: const Color(0xFFE8E8E8),
                width: 0.5,
              ),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Text(
                    '', // Empty placeholder text area
                    style: TextStyle(
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontSize: ResponsiveFontSizes.titleMedium(context),
                      color: AppColors.textGreyColor,
                    ),
                  ),
                ),
                Icon(
                  Icons.search,
                  size: 20,
                  color: AppColors.textGreyColor,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabletTabItem(String tabKey, IconData icon, String label) {
    final isSelected = _selectedTab == tabKey;

    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedTab = tabKey;
            _currentPage = 0;
          });
        },
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 14,
              color: isSelected ? AppColors.black : AppColors.textGreyColor,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontFamily: FontManager.fontFamilyTiemposText,
                fontSize: ResponsiveFontSizes.titleLarge(context),
                fontWeight: FontWeight.w400,
                color: isSelected ? AppColors.black : AppColors.textGreyColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabletCardList() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListView.builder(
        padding: const EdgeInsets.all(0),
        itemCount: _filteredData.length,
        itemBuilder: (context, index) {
          return _buildTabletCard(_filteredData[index], index);
        },
      ),
    );
  }

  Widget _buildStaticTabletCard(Map<String, dynamic> item, int index) {
    final isFavorite = item['isFavorite'] ?? false;

    return Container(
      margin: const EdgeInsets.only(bottom: 1),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Color(0xFFE8E8E8),
            width: 0.5,
          ),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            // Type icon
            _buildTypeIcon(item['type']),
            const SizedBox(width: 16),

            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item['fileName'] ?? '',
                    style: TextStyle(
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontSize: ResponsiveFontSizes.titleLarge(context),
                      fontWeight: FontWeight.w500,
                      color: AppColors.black,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    item['type'] ?? '',
                    style: TextStyle(
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontSize: ResponsiveFontSizes.titleMedium(context),
                      fontWeight: FontWeight.w400,
                      color: Color(0xFF797676),
                    ),
                  ),
                ],
              ),
            ),

            // Date - static display
            Text(
              _formatTabletDate(item['lastOpened']),
              style: TextStyle(
                  fontFamily: FontManager.fontFamilyTiemposText,
                  fontSize: ResponsiveFontSizes.titleMedium(context),
                  fontWeight: FontWeight.w300,
                  //color: AppColors.textGreyColor,
                  color: Color(0xFF797676)),
            ),

            const SizedBox(width: 45),

            // Favorite star - static display
            Icon(
              isFavorite ? Icons.star : Icons.star_border,
              size: 20,
              color: isFavorite ? Colors.amber : AppColors.textGreyColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabletCard(Map<String, dynamic> item, int index) {
    final isFavorite = item['isFavorite'] ?? false;

    return Container(
      margin: const EdgeInsets.only(bottom: 1),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Color(0xFFE8E8E8),
            width: 0.5,
          ),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 7),
        child: Row(
          children: [
            // Type icon
            _buildTypeIcon(item['type']),
            const SizedBox(width: 12),

            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item['fileName'] ?? '',
                    style: TextStyle(
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontSize: ResponsiveFontSizes.titleLarge(context),
                      fontWeight: FontWeight.w500,
                      color: AppColors.black,
                    ),
                  ),
                  const SizedBox(height: 0),
                  Text(
                    item['type'] ?? '',
                    style: TextStyle(
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontSize: ResponsiveFontSizes.titleMedium(context),
                      fontWeight: FontWeight.w400,
                      color: AppColors.textGreyColor,
                    ),
                  ),
                ],
              ),
            ),

            // Date
            Text(
              _formatTabletDate(item['lastOpened']),
              style: TextStyle(
                fontFamily: FontManager.fontFamilyTiemposText,
                fontSize: ResponsiveFontSizes.titleMedium(context),
                fontWeight: FontWeight.w400,
                color: AppColors.textGreyColor,
              ),
            ),

            const SizedBox(width: 16),

            // Favorite star
            MouseRegion(
              cursor: SystemMouseCursors.click,
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    item['isFavorite'] = !item['isFavorite'];
                  });
                },
                child: Icon(
                  isFavorite ? Icons.star : Icons.star_border,
                  size: 20,
                  color: isFavorite ? Colors.amber : AppColors.textGreyColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatTabletDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;

    if (difference == 0) {
      return 'Today';
    } else if (difference == 1) {
      return 'Yesterday';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  // Keep existing functions for non-tablet devices
  Widget _buildTabBar() {
    final tabs = [
      AppLocalizations.of(context)!.translate('mylibrary.buttonText.recent'),
      AppLocalizations.of(context)!.translate('mylibrary.buttonText.favourite')
    ];

    // Always ensure Recent tab is selected by default and when language changes
    // This ensures the first tab stays active regardless of language changes
    if (_selectedTab.isEmpty || !tabs.contains(_selectedTab)) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            _selectedTab = tabs[0]; // Always default to Recent tab
          });
        }
      });
      _selectedTab = tabs[0]; // Immediate assignment as well
    }

    return Row(
      children: [
        ...tabs.map((tab) => _buildTabItem(tab)),
        const Spacer(),
        _showSearchBar
            ? SearchBarWidget(
                controller: _searchController,
                onClose: _toggleSearchBar,
              )
            : GestureDetector(
                onTap: _toggleSearchBar,
                child: MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: Container(
                    margin: const EdgeInsets.only(right: AppSpacing.sm),
                    height: 36,
                    child: HoverableSearchIcon(),
                  ),
                ),
              ),
      ],
    );
  }

  Widget _buildTabItem(String tab) {
    final isSelected = _selectedTab == tab;

    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedTab = tab;
            _currentPage = 0; // Reset to first page when changing tabs
          });
        },
        child: Container(
          margin: const EdgeInsets.only(right: AppSpacing.xs),
          padding: const EdgeInsets.symmetric(
              horizontal: AppSpacing.md, vertical: AppSpacing.xs),
          decoration: BoxDecoration(
            color: isSelected ? AppColors.black : Colors.transparent,
            borderRadius: BorderRadius.circular(2),
          ),
          child: Text(
            tab,
            style: TextStyle(
              fontFamily: FontManager.fontFamilyTiemposText,
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontWeight: FontWeight.w400,
              color: isSelected ? Colors.white : AppColors.textGreyColor,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTable() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Color(0xffD0D0D0), width: 0.5),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        controller: _horizontalScrollController,
        child: SizedBox(
          width: _calculateTableWidth(),
          child: Column(
            children: [
              // Header
              _buildTableHeader(),
              // Content
              Expanded(
                child: ListView.builder(
                  padding: EdgeInsets.zero,
                  itemCount: _paginatedData.length,
                  itemBuilder: (context, index) {
                    return _buildTableRow(_paginatedData[index], index);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTableHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Color(0xffD0D0D0), width: 0.5),
          top: BorderSide(color: Color(0xffD0D0D0), width: 0.5),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // File Name - Left aligned
          Expanded(
            flex: 3,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Row(
                children: [
                  Visibility(
                    visible: false,
                    maintainSize: true,
                    maintainAnimation: true,
                    maintainState: true,
                    child: CustomImage.asset(
                      'assets/icons/default.svg',
                      width: 32,
                      height: 32,
                      fit: BoxFit.contain,
                    ).toWidget(),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    AppLocalizations.of(context)!
                        .translate('mylibrary.TableHeaderText.fileName'),
                    style: TextStyle(
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontSize: ResponsiveFontSizes.titleSmall(context),
                      fontWeight: FontWeight.w400,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Last Opened
          Expanded(
            flex: 2,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                AppLocalizations.of(context)!
                    .translate('mylibrary.TableHeaderText.lastOpened'),
                style: TextStyle(
                  fontFamily: FontManager.fontFamilyTiemposText,
                  fontSize: ResponsiveFontSizes.titleSmall(context),
                  fontWeight: FontWeight.w400,
                  color: Colors.black,
                ),
              ),
            ),
          ),
          // Favorites column - empty header
          Expanded(
            flex: 1,
            child: Container(),
          ),
          // Sort With label
          Expanded(
            flex: 1,
            child: Align(
              alignment: Alignment.centerRight,
              child: Text(
                AppLocalizations.of(context)!
                    .translate('mylibrary.TableHeaderText.sortWith'),
                style: TextStyle(
                  fontFamily: FontManager.fontFamilyTiemposText,
                  fontSize: ResponsiveFontSizes.titleSmall(context),
                  fontWeight: FontWeight.w500,
                  color: AppColors.textGreyColor,
                ),
              ),
            ),
          ),
          // Right group - Project, Solution, Object, Role, Draft, Published
          Expanded(
            flex: 5, // Increased flex to accommodate new columns
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                    child: Center(
                        child: _buildSortableHeader(
                            AppLocalizations.of(context)!
                                .translate('mylibrary.TableHeaderText.project'),
                            'project'))),
                Expanded(
                    child: Center(
                        child: _buildSortableHeader(
                            AppLocalizations.of(context)!.translate(
                                'mylibrary.TableHeaderText.solution'),
                            'solution'))),
                Expanded(
                    child: Center(
                        child: _buildSortableHeader(
                            AppLocalizations.of(context)!
                                .translate('mylibrary.TableHeaderText.object'),
                            'object'))),
                Expanded(
                    child: Center(
                        child: _buildSortableHeader(
                            AppLocalizations.of(context)!
                                .translate('mylibrary.TableHeaderText.role'),
                            'role'))),
                Expanded(
                    child: Center(
                        child: _buildSortableHeader(
                            AppLocalizations.of(context)!
                                .translate('mylibrary.TableHeaderText.draft'),
                            'draft'))),
                Expanded(
                    child: Center(
                        child: _buildSortableHeader(
                            AppLocalizations.of(context)!.translate(
                                'mylibrary.TableHeaderText.published'),
                            'published'))),
              ],
            ),
          ),
        ],
      ),
    );
  } // Updated _buildSortableHeader method

  Widget _buildSortableHeader(String title, String column) {
    // Allow filtering for category columns and status columns
    final isFilterableColumn = [
      'project',
      'solution',
      'object',
      'role',
      'draft',
      'published'
    ].contains(column);

    final isCurrentFilter =
        _selectedCategoryFilter == _getCategoryFromColumn(column);
    final isHovered = _hoveredHeaderColumn == column;

    return MouseRegion(
      cursor: isFilterableColumn
          ? SystemMouseCursors.click
          : SystemMouseCursors.basic,
      onEnter: (_) => setState(() => _hoveredHeaderColumn = column),
      onExit: (_) => setState(() => _hoveredHeaderColumn = null),
      child: GestureDetector(
        onTap: isFilterableColumn ? () => _filterByCategory(column) : null,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              title,
              style: TextStyle(
                fontFamily: FontManager.fontFamilyTiemposText,
                fontSize: ResponsiveFontSizes.titleSmall(context),
                fontWeight: FontWeight.w400,
                color: isCurrentFilter ? Colors.black : AppColors.textGreyColor,
              ),
            ),
            const SizedBox(width: 4),
            SizedBox(
              width: 10,
              height: 10,
              child: isFilterableColumn && isHovered
                  ? SvgPicture.asset(
                      'assets/images/my_library/my_library_sort.svg',
                      width: 10,
                      height: 10,
                    )
                  : const SizedBox.shrink(),
            ),
          ],
        ),
      ),
    );
  }

// Updated _getCategoryFromColumn method
  String? _getCategoryFromColumn(String column) {
    switch (column) {
      case 'project':
        return 'Project';
      case 'solution':
        return 'Solution';
      case 'object':
        return 'Object';
      case 'role':
        return 'Role';
      case 'draft':
        return 'Draft';
      case 'published':
        return 'Published';
      default:
        return null;
    }
  }

// Updated _filterByCategory method to handle status filtering
  void _filterByCategory(String column) {
    setState(() {
      if (column == 'draft' || column == 'published') {
        // Handle status filtering
        final status = column == 'draft' ? 'Draft' : 'Published';
        if (_selectedCategoryFilter == status) {
          _selectedCategoryFilter = null;
        } else {
          _selectedCategoryFilter = status;
        }
      } else {
        // Handle type filtering
        final category = _getCategoryFromColumn(column);
        if (_selectedCategoryFilter == category) {
          _selectedCategoryFilter = null;
        } else {
          _selectedCategoryFilter = category;
        }
      }
      _currentPage = 0;
    });
  }

// Updated _buildTableRow method
  Widget _buildTableRow(Map<String, dynamic> item, int index) {
    final isEvenRow = index % 2 == 0;
    final isHovered = _hoveredRowIndex == index;
    final isFavorite = item['isFavorite'] ?? false;
    final status = item['status'] ?? '';

    return MouseRegion(
      onEnter: (_) => setState(() => _hoveredRowIndex = index),
      onExit: (_) => setState(() => _hoveredRowIndex = null),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        decoration: BoxDecoration(
          color: isEvenRow ? Colors.grey.shade50 : Colors.white,
          border: Border(
            bottom: BorderSide(color: Color(0xffD0D0D0), width: 0.5),
          ),
        ),
        child: Row(
          children: [
            // File Name - Left aligned
            Expanded(
              flex: 3,
              child: Row(
                children: [
                  _buildTypeIcon(item['type']),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item['fileName'] ?? '',
                          style: TextStyle(
                            fontFamily: FontManager.fontFamilyTiemposText,
                            fontSize: ResponsiveFontSizes.titleLarge(context),
                            fontWeight: FontWeight.w500,
                            color: AppColors.textPrimaryLight,
                          ),
                        ),
                        if (item['type'].isNotEmpty)
                          Text(
                            item['type'] ?? '',
                            style: TextStyle(
                              fontFamily: FontManager.fontFamilyTiemposText,
                              fontSize: ResponsiveFontSizes.titleSmall(context),
                              fontWeight: FontWeight.w400,
                              color: AppColors.textGreyColor,
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            // Last Opened
            Expanded(
              flex: 2,
              child: Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  _formatDate(item['lastOpened']),
                  style: TextStyle(
                    fontFamily: FontManager.fontFamilyTiemposText,
                    fontSize: ResponsiveFontSizes.titleSmall(context),
                    fontWeight: FontWeight.w400,
                    color: AppColors.textGreyColor,
                  ),
                ),
              ),
            ),
            // Favorites column
            Expanded(
              flex: 1,
              child: Align(
                alignment: Alignment.centerRight,
                child: Padding(
                  padding: const EdgeInsets.only(right: 40.0),
                  child: Visibility(
                    visible: isHovered || isFavorite,
                    child: MouseRegion(
                      cursor: SystemMouseCursors.click,
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            item['isFavorite'] = !item['isFavorite'];
                          });
                        },
                        child: Icon(
                          item['isFavorite'] ? Icons.star : Icons.star_border,
                          size: 20,
                          color: item['isFavorite']
                              ? Colors.amber
                              : AppColors.textGreyColor,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
            // Sort With spacer
            Expanded(
              flex: 1,
              child: Container(),
            ),
            // Status columns section
            Expanded(
              flex: 5,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // Project column
                  Expanded(child: Container()),
                  // Solution column
                  Expanded(child: Container()),
                  // Object column
                  Expanded(child: Container()),
                  // Role column
                  Expanded(child: Container()),
                  // Draft column
                  Expanded(
                    child: Center(
                      child: status == 'Draft'
                          ? _buildStatusBadge('Draft', Colors.orange)
                          : Container(),
                    ),
                  ),
                  // Published column
                  Expanded(
                    child: Center(
                      child: status == 'Published'
                          ? _buildStatusBadge('Published', Colors.blue)
                          : Container(),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

// New method to build status badges
  Widget _buildStatusBadge(String status, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        status,
        style: TextStyle(
          fontFamily: FontManager.fontFamilyTiemposText,
          fontSize: ResponsiveFontSizes.titleSmall(context),
          fontWeight: FontWeight.w500,
          color: Colors.white,
        ),
      ),
    );
  }

  Widget _buildTypeIcon(String type) {
    String svgAsset;

    switch (type) {
      case 'Role':
        svgAsset = 'assets/images/my_library/create_role.svg';
        break;
      case 'Object':
        svgAsset = 'assets/images/my_library/create_object.svg';
        break;
      case 'Solution':
        svgAsset = 'assets/images/my_library/create_solution.svg';
        break;
      case 'Project':
        svgAsset = 'assets/images/my_library/create_project.svg';
        break;
      default:
        svgAsset = 'assets/icons/default.svg';
    }

    return Center(
      child: CustomImage.asset(
        svgAsset,
        width: 32,
        height: 32,
        fit: BoxFit.contain,
      ).toWidget(),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;

    if (difference == 0) {
      return 'Today';
    } else if (difference == 1) {
      return 'Yesterday, ${date.day}/${date.month}/${date.year}';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  Widget _buildPagination() {
    if (_totalPages <= 1) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(top: AppSpacing.md),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          // Navigation buttons
          Row(
            children: [
              // Previous button
              _HoverPaginationButton(
                icon: const Icon(Icons.chevron_left, size: 20),
                onPressed: _currentPage > 0
                    ? () {
                        setState(() {
                          _currentPage--;
                        });
                      }
                    : null,
              ),
              const SizedBox(width: 8),
              // Next button
              _HoverPaginationButton(
                icon: const Icon(Icons.chevron_right, size: 20),
                onPressed: _currentPage < _totalPages - 1
                    ? () {
                        setState(() {
                          _currentPage++;
                        });
                      }
                    : null,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFullScreenDiscoverModal() {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth >= 600 && screenWidth <= 1023;

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Stack(
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(160, 75, 160, 75),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with close button
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(top: 85),
                          child: Text.rich(
                            TextSpan(
                              children: [
                                TextSpan(
                                  text: 'How Discover Works -\n',
                                  style: TextStyle(
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    fontSize: 20,
                                    fontWeight: FontWeight.w500,
                                    color: AppColors.textPrimaryLight,
                                    height: 1.3,
                                  ),
                                ),
                                TextSpan(
                                  text: 'Your AI-Guided Journey',
                                  style: TextStyle(
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    fontSize: 20,
                                    fontWeight: FontWeight.w700, // More bold
                                    color: AppColors.textPrimaryLight,
                                    height: 1.5,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      // Only show close button here for non-tablet devices
                      if (!isTablet)
                        MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: () => Navigator.of(context).pop(),
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Icon(
                                Icons.close,
                                size: 24,
                                color: AppColors.textGreyColor,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 30),
                  // Content
                  Expanded(
                    child: SingleChildScrollView(
                      child: _buildDiscoverModalContent(),
                    ),
                  ),
                ],
              ),
            ),
            // Positioned close button for tablet devices only
            if (isTablet)
              Positioned(
                top: 75,
                right: 110,
                child: MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: GestureDetector(
                    onTap: () => Navigator.of(context).pop(),
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Icon(
                        Icons.close,
                        size: 24,
                        color: AppColors.textGreyColor,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildFullScreenDevelopModal() {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth >= 600 && screenWidth <= 1023;

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Stack(
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(160, 75, 160, 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with close button
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(top: 85),
                          child: Text.rich(
                            TextSpan(
                              children: [
                                TextSpan(
                                  text: 'How Development Works -\n',
                                  style: TextStyle(
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    fontSize: 20,
                                    fontWeight: FontWeight.w500,
                                    color: AppColors.textPrimaryLight,
                                    height: 1.5,
                                  ),
                                ),
                                TextSpan(
                                  text: 'Your Custom Build Journey',
                                  style: TextStyle(
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    fontSize: 20,
                                    fontWeight: FontWeight.w700, // More bold
                                    color: AppColors.textPrimaryLight,
                                    height: 1.3,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      // Only show close button here for non-tablet devices
                      if (!isTablet)
                        MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: () => Navigator.of(context).pop(),
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Icon(
                                Icons.close,
                                size: 24,
                                color: AppColors.textGreyColor,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 30),
                  // Content
                  Expanded(
                    child: SingleChildScrollView(
                      child: _buildDevelopModalContent(),
                    ),
                  ),
                ],
              ),
            ),
            // Positioned close button for tablet devices only
            if (isTablet)
              Positioned(
                top: 75,
                right: 110,
                child: MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: GestureDetector(
                    onTap: () => Navigator.of(context).pop(),
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Icon(
                        Icons.close,
                        size: 24,
                        color: AppColors.textGreyColor,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildFullScreenRecentModal() {
    return StatefulBuilder(
      builder: (context, setModalState) {
        return Scaffold(
          backgroundColor: Color(0xFFF7F9FB),
          body: SafeArea(
            child: Column(
              children: [
                // Header section
                Container(
                  //padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  margin: const EdgeInsets.fromLTRB(24, 30, 24, 0),
                  child: Column(
                    children: [
                      // Top row with back arrow, title, and controls
                      Row(
                        children: [
                          // Back arrow
                          MouseRegion(
                            cursor: SystemMouseCursors.click,
                            child: GestureDetector(
                              onTap: () => Navigator.of(context).pop(),
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Icon(
                                  Icons.arrow_back_ios,
                                  size: 18,
                                  color: AppColors.black,
                                ),
                              ),
                            ),
                          ),

                          // Centered title
                          Expanded(
                            child: Center(
                              child: Text(
                                AppLocalizations.of(context)!
                                    .translate('mylibrary.buttonText.recent'),
                                style: TextStyle(
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  fontSize: 18,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF242424),
                                ),
                              ),
                            ),
                          ),

                          // Right side controls (empty for balance)
                          const SizedBox(width: 34), // Balance the back arrow
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Search bar row
                      Row(
                        children: [
                          // Search bar (taking most space)
                          Expanded(
                            child: Container(
                              height: 48,
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 12),
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: Color(0xFFCCCCCC),
                                  width: .5,
                                ),
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: TextField(
                                      decoration: InputDecoration(
                                        border: InputBorder.none,
                                        enabledBorder: InputBorder.none,
                                        focusedBorder: InputBorder.none,
                                        disabledBorder: InputBorder.none,
                                        errorBorder: InputBorder.none,
                                        focusedErrorBorder: InputBorder.none,
                                        hintText: '',
                                        contentPadding: EdgeInsets.zero,
                                        isDense: true,
                                        filled: false,
                                      ),
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontFamily:
                                            FontManager.fontFamilyTiemposText,
                                      ),
                                    ),
                                  ),
                                  Icon(
                                    Icons.search,
                                    size: 22,
                                    color: Color(0xFF7C7C7C),
                                  ),
                                ],
                              ),
                            ),
                          ),

                          const SizedBox(width: 16),

                          // Sort icon
                          MouseRegion(
                            cursor: SystemMouseCursors.click,
                            child: GestureDetector(
                              onTap: () => _showRecentFilterModalWithCallback(
                                  setModalState),
                              child: Container(
                                width: 48,
                                height: 48,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(6),
                                  border: Border.all(
                                    color: const Color(0xFFCCCCCC),
                                    width: .5,
                                  ),
                                ),
                                child: const Center(
                                  child: Icon(
                                    Icons.swap_vert,
                                    size: 18,
                                    color: Color(
                                        0xFF000000), // Equivalent to a text grey
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Content area
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.fromLTRB(24, 16, 24, 16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(2),
                      border: Border.all(
                        color: Color(0xFFCCCCCC),
                        width: .5,
                      ),
                    ),
                    child: _buildRecentModalContent(),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildFullScreenFavouriteModal() {
    return StatefulBuilder(
      builder: (context, setModalState) {
        return Scaffold(
          backgroundColor: Color(0xFFF7F9FB),
          body: SafeArea(
            child: Column(
              children: [
                // Header section
                Container(
                  margin: const EdgeInsets.fromLTRB(24, 30, 24, 0),
                  child: Column(
                    children: [
                      // Top row with back arrow, title, and controls
                      Row(
                        children: [
                          // Back arrow
                          MouseRegion(
                            cursor: SystemMouseCursors.click,
                            child: GestureDetector(
                              onTap: () => Navigator.of(context).pop(),
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Icon(
                                  Icons.arrow_back_ios,
                                  size: 18,
                                  color: AppColors.black,
                                ),
                              ),
                            ),
                          ),

                          // Centered title
                          Expanded(
                            child: Center(
                              child: Text(
                                AppLocalizations.of(context)!.translate(
                                    'mylibrary.buttonText.favourite'),
                                style: TextStyle(
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  fontSize: 18,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF242424),
                                ),
                              ),
                            ),
                          ),

                          // Right side controls (empty for balance)
                          const SizedBox(width: 34), // Balance the back arrow
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Search bar row
                      Row(
                        children: [
                          // Search bar (taking most space)
                          Expanded(
                            child: Container(
                              height: 48,
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 12),
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: Color(0xFFCCCCCC),
                                  width: .5,
                                ),
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: TextField(
                                      decoration: InputDecoration(
                                        border: InputBorder.none,
                                        enabledBorder: InputBorder.none,
                                        focusedBorder: InputBorder.none,
                                        disabledBorder: InputBorder.none,
                                        errorBorder: InputBorder.none,
                                        focusedErrorBorder: InputBorder.none,
                                        hintText: '',
                                        contentPadding: EdgeInsets.zero,
                                        isDense: true,
                                        filled: false,
                                      ),
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontFamily:
                                            FontManager.fontFamilyTiemposText,
                                      ),
                                    ),
                                  ),
                                  Icon(
                                    Icons.search,
                                    size: 22,
                                    color: Color(0xFF7C7C7C),
                                  ),
                                ],
                              ),
                            ),
                          ),

                          const SizedBox(width: 16),

                          // Sort icon
                          MouseRegion(
                            cursor: SystemMouseCursors.click,
                            child: GestureDetector(
                              onTap: () =>
                                  _showFavouriteFilterModalWithCallback(
                                      setModalState),
                              child: Container(
                                width: 48,
                                height: 48,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(6),
                                  border: Border.all(
                                    color: const Color(0xFFCCCCCC),
                                    width: .5,
                                  ),
                                ),
                                child: const Center(
                                  child: Icon(
                                    Icons.swap_vert,
                                    size: 18,
                                    color: Color(0xFF000000),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Content area
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.fromLTRB(24, 16, 24, 16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(2),
                      border: Border.all(
                        color: Color(0xFFCCCCCC),
                        width: .5,
                      ),
                    ),
                    child: _buildFavouriteModalContent(),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildFullScreenChatModal() {
    return Scaffold(
      backgroundColor: Color(0xFFF7F9FB),
      body: SafeArea(
        child: Column(
          children: [
            // Header section
            Container(
              margin: const EdgeInsets.fromLTRB(24, 30, 24, 0),
              child: Column(
                children: [
                  // Top row with back arrow, title, and controls
                  Row(
                    children: [
                      // Back arrow
                      MouseRegion(
                        cursor: SystemMouseCursors.click,
                        child: GestureDetector(
                          onTap: () => Navigator.of(context).pop(),
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Icon(
                              Icons.arrow_back_ios,
                              size: 18,
                              color: AppColors.black,
                            ),
                          ),
                        ),
                      ),

                      // Centered title
                      Expanded(
                        child: Center(
                          child: Text(
                            'Chat',
                            style: TextStyle(
                              fontFamily: FontManager.fontFamilyTiemposText,
                              fontSize: 18,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFF242424),
                            ),
                          ),
                        ),
                      ),

                      // Right side controls (empty for balance)
                      const SizedBox(width: 34), // Balance the back arrow
                    ],
                  ),
                ],
              ),
            ),

            // Chat area - takes up most of the space
            Expanded(
              child: Container(
                margin: const EdgeInsets.fromLTRB(24, 16, 24, 0),
                child: Column(
                  children: [
                    // Chat messages area (empty for now)
                    Expanded(
                      child: Container(
                          // Empty chat area
                          ),
                    ),

                    // Chat input area at bottom
                    Container(
                      margin: const EdgeInsets.only(bottom: 24),
                      height: 140,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Color(0xFFE0E0E0),
                          width: 0.5,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Color(
                                0x249B9B9B), // #9B9B9B24 with correct alpha placement
                            blurRadius: 8,
                            offset: Offset(
                                0, 4), // Optional: adjust for elevation effect
                          ),
                        ],
                      ),
                      child: Stack(
                        children: [
                          // Text input field - positioned at top left
                          Positioned(
                            left: 15,
                            top: 15,
                            right: 15,
                            child: TextField(
                              maxLines: null,
                              decoration: InputDecoration(
                                hintText: 'Type you reply',
                                hintStyle: TextStyle(
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w400,
                                  color: Color(0xFF999999),
                                ),
                                border: InputBorder.none,
                                enabledBorder: InputBorder.none,
                                focusedBorder: InputBorder.none,
                                disabledBorder: InputBorder.none,
                                errorBorder: InputBorder.none,
                                focusedErrorBorder: InputBorder.none,
                                contentPadding: EdgeInsets.zero,
                                isDense: false,
                                filled: false,
                                fillColor: Colors.transparent,
                                hoverColor: Colors.transparent,
                              ),
                              style: TextStyle(
                                fontFamily: FontManager.fontFamilyTiemposText,
                                fontSize: 16,
                                fontWeight: FontWeight.w400,
                                color: Color(0xFF333333),
                                height: 1.5,
                              ),
                            ),
                          ),

                          // Plus button - positioned at bottom left
                          Positioned(
                            left: 16,
                            bottom: 16,
                            child: Icon(
                              Icons.add,
                              size: 20,
                              color: Color(0xFF666666),
                            ),
                          ),

                          // Microphone button - positioned at bottom right
                          Positioned(
                            right: 16,
                            bottom: 16,
                            child: Icon(
                              Icons.mic,
                              size: 20,
                              color: Color(0xFF666666),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterModalContent() {
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Recent option (changed from Newest)
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(
                bottom: BorderSide(
                  color: Color(0xFFE8E8E8),
                  width: 0.5,
                ),
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              child: Row(
                children: [
                  // Square checkbox
                  Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: Color(0xFFCCCCCC),
                        width: 1,
                      ),
                      borderRadius: BorderRadius.circular(2),
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Recent',
                    style: TextStyle(
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                      color: Color(0xFF333333),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Relevance option
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(
                bottom: BorderSide(
                  color: Color(0xFFE8E8E8),
                  width: 0.5,
                ),
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              child: Row(
                children: [
                  // Square checkbox
                  Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: Color(0xFFCCCCCC),
                        width: 1,
                      ),
                      borderRadius: BorderRadius.circular(2),
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Relevance',
                    style: TextStyle(
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                      color: Color(0xFF333333),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Objects option
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(
                bottom: BorderSide(
                  color: Color(0xFFE8E8E8),
                  width: 0.5,
                ),
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              child: Row(
                children: [
                  // Square checkbox
                  Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: Color(0xFFCCCCCC),
                        width: 1,
                      ),
                      borderRadius: BorderRadius.circular(2),
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Objects',
                    style: TextStyle(
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                      color: Color(0xFF333333),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Alphabetical option
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              child: Row(
                children: [
                  // Square checkbox
                  Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: Color(0xFFCCCCCC),
                        width: 1,
                      ),
                      borderRadius: BorderRadius.circular(2),
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Alphabetical',
                    style: TextStyle(
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                      color: Color(0xFF333333),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterOptionWithIcon({
    required String icon,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border(
              bottom: BorderSide(
                color: Color(0xFFE8E8E8),
                width: 0.5,
              ),
            ),
          ),
          child: Row(
            children: [
              // Square checkbox
              Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Color(0xFFCCCCCC),
                    width: 1,
                  ),
                  borderRadius: BorderRadius.circular(3),
                  color: isSelected ? Color(0xFF007AFF) : Colors.white,
                ),
                child: isSelected
                    ? Icon(
                        Icons.check,
                        size: 14,
                        color: Colors.white,
                      )
                    : null,
              ),
              const SizedBox(width: 12),

              // SVG Icon
              SvgPicture.asset(
                icon,
                width: 20,
                height: 20,
              ),
              const SizedBox(width: 12),

              // Label
              Text(
                label,
                style: TextStyle(
                  fontFamily: FontManager.fontFamilyTiemposText,
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                  color: Color(0xFF333333),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSimpleFilterOption({
    required String icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border(
              bottom: BorderSide(
                color: Color(0xFFE8E8E8),
                width: 0.5,
              ),
            ),
          ),
          child: Row(
            children: [
              // SVG Icon - no checkbox
              SvgPicture.asset(
                icon,
                width: 20,
                height: 20,
              ),
              const SizedBox(width: 12),

              // Label
              Text(
                label,
                style: TextStyle(
                  fontFamily: FontManager.fontFamilyTiemposText,
                  fontSize: ResponsiveFontSizes.titleLarge(context),
                  fontWeight: FontWeight.w400,
                  color: Colors.black,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFilterOption({
    required IconData icon,
    required String label,
    required bool isSelected,
  }) {
    return Row(
      children: [
        // Square checkbox
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            border: Border.all(
              color: Color(0xFFCCCCCC),
              width: 1,
            ),
            borderRadius: BorderRadius.circular(2),
            color: isSelected ? Color(0xFF007AFF) : Colors.white,
          ),
          child: isSelected
              ? Icon(
                  Icons.check,
                  size: 12,
                  color: Colors.white,
                )
              : null,
        ),
        const SizedBox(width: 8),

        // Icon
        Icon(
          icon,
          size: 16,
          color: Color(0xFF666666),
        ),
        const SizedBox(width: 8),

        // Label
        Text(
          label,
          style: TextStyle(
            fontFamily: FontManager.fontFamilyTiemposText,
            fontSize: 14,
            fontWeight: FontWeight.w400,
            color: Color(0xFF333333),
          ),
        ),
      ],
    );
  }

  Widget _buildModal({required String title, required Widget content}) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: 600,
        constraints: const BoxConstraints(maxHeight: 500),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(24),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: AppColors.greyBorder, width: 1),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      title,
                      style: TextStyle(
                        fontFamily: FontManager.fontFamilyTiemposText,
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimaryLight,
                      ),
                    ),
                  ),
                  MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                      onTap: () => Navigator.of(context).pop(),
                      child: Icon(
                        Icons.close,
                        size: 24,
                        color: AppColors.textGreyColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // Content
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: content,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDiscoverModalContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildModalStep(
          'Step 1: Describe Your solution',
          'Specify your business industry, organization size, operational requirements, and geographic locations for the solution.',
        ),
        const SizedBox(height: 16),
        _buildModalStep(
          'Step 2: AI Analysis',
          'Our AI engine automatically discovers and lists out the roles, entities, and workflows for your solution',
        ),
        const SizedBox(height: 16),
        _buildModalStep(
          'Step 3: Review & Customize',
          'Validate the document in details and customize any components you want to modify with prompt',
        ),
        const SizedBox(height: 16),
        _buildModalStep(
          'Step 4: Development',
          'Once you have confirmed the final components, proceed with the development of your solution using the discovered framework',
        ),
        const SizedBox(height: 16),
        _buildModalStep(
          'Step 5: Testing',
          'Test your completed solution to ensure it works as expected.',
        ),
      ],
    );
  }

  Widget _buildDevelopModalContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildModalStep(
          'Step 1: Requirements Definition',
          'Craft your detailed solution requirements through our intuitive prompt interface, providing comprehensive information about your business needs.',
        ),
        const SizedBox(height: 16),
        _buildModalStep(
          'Step 2: Intelligent Parsing',
          'Our AI engine intelligently extracts and identifies the essential components from your comprehensive requirements.',
        ),
        const SizedBox(height: 16),
        _buildModalStep(
          'Step 3: Solution Architecture',
          'Seamlessly add new components and fine-tune every aspect of your solution framework to perfectly match your vision.',
        ),
        const SizedBox(height: 16),
        _buildModalStep(
          'Step 4: AI-Powered Optimization',
          'Leverage smart AI recommendations to enhance and optimize your solution design for maximum effectiveness.',
        ),
        const SizedBox(height: 16),
        _buildModalStep(
          'Step 5: Industry Alignment',
          'Effortlessly import industry-standard components and best practices tailored to your business sector.',
        ),
        const SizedBox(height: 16),
        _buildModalStep(
          'Step 6: Solution Validation',
          'Thoroughly review your complete solution framework, including all components and workflow structures.',
        ),
        const SizedBox(height: 16),
        _buildModalStep(
          'Step 7: Implementation',
          '',
        ),
      ],
    );
  }

  Widget _buildModalStep(String title, String description) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontFamily: FontManager.fontFamilyTiemposText,
            fontSize: ResponsiveFontSizes.titleLarge(context),
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimaryLight,
          ),
        ),
        if (description.isNotEmpty) ...[
          const SizedBox(height: 4),
          Text(
            description,
            style: TextStyle(
              fontFamily: FontManager.fontFamilyTiemposText,
              fontSize: ResponsiveFontSizes.titleLarge(context),
              fontWeight: FontWeight.w400,
              color: AppColors.textGreyColor,
              height: 1.4,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildRecentModalContent() {
    // Get filtered data for Recent tab using separate Recent filter
    List<Map<String, dynamic>> recentFilteredData = List.from(_allData);

    // Apply Recent modal's category filter if selected
    if (_selectedRecentFilter != null) {
      if (_selectedRecentFilter == 'Draft' ||
          _selectedRecentFilter == 'Published') {
        // Filter by status
        recentFilteredData = recentFilteredData
            .where((item) => item['status'] == _selectedRecentFilter)
            .toList();
      } else {
        // Filter by type
        recentFilteredData = recentFilteredData
            .where((item) => item['type'] == _selectedRecentFilter)
            .toList();
      }
    }

    // Sort by last opened (most recent first)
    recentFilteredData.sort((a, b) {
      DateTime aDate = a['lastOpened'];
      DateTime bDate = b['lastOpened'];
      return bDate.compareTo(aDate);
    });

    return Container(
      color: Colors.white,
      child: ListView.builder(
        padding: const EdgeInsets.all(0),
        itemCount: recentFilteredData.length,
        itemBuilder: (context, index) {
          return _buildRecentModalCard(recentFilteredData[index], index);
        },
      ),
    );
  }

  Widget _buildRecentModalCard(Map<String, dynamic> item, int index) {
    final isFavorite = item['isFavorite'] ?? false;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Color(0xFFE8E8E8),
            width: 0.5,
          ),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          children: [
            // Type icon
            _buildTypeIcon(item['type']),
            const SizedBox(width: 16),

            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item['fileName'] ?? '',
                    style: TextStyle(
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontSize: ResponsiveFontSizes.titleLarge(context),
                      fontWeight: FontWeight.w500,
                      color: AppColors.black,
                    ),
                  ),
                  const SizedBox(height: 0),
                  Text(
                    item['type'] ?? '',
                    style: TextStyle(
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontSize: ResponsiveFontSizes.titleMedium(context),
                      fontWeight: FontWeight.w400,
                      color: Color(0xFF797676),
                    ),
                  ),
                ],
              ),
            ),

            // Date
            Text(
              _formatTabletDate(item['lastOpened']),
              style: TextStyle(
                fontFamily: FontManager.fontFamilyTiemposText,
                fontSize: ResponsiveFontSizes.titleMedium(context),
                fontWeight: FontWeight.w400,
                color: Color(0xFF797676),
              ),
            ),

            const SizedBox(width: 24),

            // Favorite star
            Icon(
              isFavorite ? Icons.star : Icons.star_border,
              size: 20,
              color: isFavorite ? Colors.amber : AppColors.textGreyColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFavouriteModalContent() {
    // Get filtered data for Favourite tab using separate Favourite filter
    List<Map<String, dynamic>> favouriteFilteredData =
        _allData.where((item) => item['isFavorite'] == true).toList();

    // Apply Favourite modal's category filter if selected
    if (_selectedFavouriteFilter != null) {
      if (_selectedFavouriteFilter == 'Draft' ||
          _selectedFavouriteFilter == 'Published') {
        // Filter by status
        favouriteFilteredData = favouriteFilteredData
            .where((item) => item['status'] == _selectedFavouriteFilter)
            .toList();
      } else {
        // Filter by type
        favouriteFilteredData = favouriteFilteredData
            .where((item) => item['type'] == _selectedFavouriteFilter)
            .toList();
      }
    }

    // Sort by last opened (most recent first)
    favouriteFilteredData.sort((a, b) {
      DateTime aDate = a['lastOpened'];
      DateTime bDate = b['lastOpened'];
      return bDate.compareTo(aDate);
    });

    return Container(
      color: Colors.white,
      child: ListView.builder(
        padding: const EdgeInsets.all(0),
        itemCount: favouriteFilteredData.length,
        itemBuilder: (context, index) {
          return _buildFavouriteModalCard(favouriteFilteredData[index], index);
        },
      ),
    );
  }

  Widget _buildFavouriteModalCard(Map<String, dynamic> item, int index) {
    final isFavorite = item['isFavorite'] ?? false;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Color(0xFFE8E8E8),
            width: 0.5,
          ),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          children: [
            // Type icon
            _buildTypeIcon(item['type']),
            const SizedBox(width: 16),

            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item['fileName'] ?? '',
                    style: TextStyle(
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontSize: ResponsiveFontSizes.titleLarge(context),
                      fontWeight: FontWeight.w500,
                      color: AppColors.black,
                    ),
                  ),
                  const SizedBox(height: 0),
                  Text(
                    item['type'] ?? '',
                    style: TextStyle(
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontSize: ResponsiveFontSizes.titleMedium(context),
                      fontWeight: FontWeight.w400,
                      color: Color(0xFF797676),
                    ),
                  ),
                ],
              ),
            ),

            // Date
            Text(
              _formatTabletDate(item['lastOpened']),
              style: TextStyle(
                fontFamily: FontManager.fontFamilyTiemposText,
                fontSize: ResponsiveFontSizes.titleMedium(context),
                fontWeight: FontWeight.w400,
                color: Color(0xFF797676),
              ),
            ),

            const SizedBox(width: 24),

            // Favorite star (always filled since these are favorites)
            Icon(
              Icons.star,
              size: 20,
              color: Colors.amber,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChatModalContent() {
    // Static data for chat items (similar to recent but with chat-related content)
    final chatItems = [
      {
        'fileName': 'Project Discussion',
        'type': 'Chat',
        'lastOpened': DateTime(2025, 9, 24),
        'isFavorite': false,
      },
      {
        'fileName': 'Solution Planning',
        'type': 'Chat',
        'lastOpened': DateTime(2025, 9, 23),
        'isFavorite': true,
      },
      {
        'fileName': 'Team Meeting Notes',
        'type': 'Chat',
        'lastOpened': DateTime(2025, 9, 22),
        'isFavorite': false,
      },
      {
        'fileName': 'Requirements Review',
        'type': 'Chat',
        'lastOpened': DateTime(2025, 9, 21),
        'isFavorite': false,
      },
    ];

    return Container(
      color: Colors.white,
      child: ListView.builder(
        padding: const EdgeInsets.all(0),
        itemCount: chatItems.length,
        itemBuilder: (context, index) {
          return _buildChatModalCard(chatItems[index], index);
        },
      ),
    );
  }

  Widget _buildChatModalCard(Map<String, dynamic> item, int index) {
    final isFavorite = item['isFavorite'] ?? false;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Color(0xFFE8E8E8),
            width: 0.5,
          ),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          children: [
            // Chat icon
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: Color(0xFFF0F0F0),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Icon(
                Icons.chat_bubble_outline,
                size: 18,
                color: AppColors.black,
              ),
            ),
            const SizedBox(width: 16),

            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item['fileName'] ?? '',
                    style: TextStyle(
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontSize: ResponsiveFontSizes.titleLarge(context),
                      fontWeight: FontWeight.w500,
                      color: AppColors.black,
                    ),
                  ),
                  const SizedBox(height: 0),
                  Text(
                    item['type'] ?? '',
                    style: TextStyle(
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontSize: ResponsiveFontSizes.titleMedium(context),
                      fontWeight: FontWeight.w400,
                      color: Color(0xFF797676),
                    ),
                  ),
                ],
              ),
            ),

            // Date
            Text(
              _formatTabletDate(item['lastOpened']),
              style: TextStyle(
                fontFamily: FontManager.fontFamilyTiemposText,
                fontSize: ResponsiveFontSizes.titleMedium(context),
                fontWeight: FontWeight.w400,
                color: Color(0xFF797676),
              ),
            ),

            const SizedBox(width: 24),

            // Favorite star
            Icon(
              isFavorite ? Icons.star : Icons.star_border,
              size: 20,
              color: isFavorite ? Colors.amber : AppColors.textGreyColor,
            ),
          ],
        ),
      ),
    );
  }
}

/// Search bar widget that appears when search icon is clicked
class SearchBarWidget extends StatelessWidget {
  final TextEditingController controller;
  final VoidCallback onClose;

  const SearchBarWidget({
    super.key,
    required this.controller,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 250,
      height: 36,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          // Search input field
          Expanded(
            child: TextField(
              controller: controller,
              decoration: InputDecoration(
                hintText: 'Search',
                hintStyle: TextStyle(
                  color: Colors.grey.shade500,
                  fontSize: ResponsiveFontSizes.titleLarge(context),
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 8.0, vertical: 0),
                isDense: true,
                hoverColor: Colors.transparent,
              ),
              style: TextStyle(
                fontSize: ResponsiveFontSizes.titleLarge(context),
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
              // Auto-focus when search bar appears
              autofocus: true,
            ),
          ),

          // Close button
          GestureDetector(
            onTap: onClose,
            child: MouseRegion(
              cursor: SystemMouseCursors.click,
              child: Padding(
                padding: const EdgeInsets.only(right: 8.0),
                child: Icon(Icons.close, size: 18, color: Colors.grey.shade600),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class HoverableSearchIcon extends StatefulWidget {
  const HoverableSearchIcon({super.key});

  @override
  _HoverableSearchIconState createState() => _HoverableSearchIconState();
}

class _HoverableSearchIconState extends State<HoverableSearchIcon> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      cursor: SystemMouseCursors.click,
      child: CustomImage.asset(
        'assets/images/my_library/my_library_search.svg',
        width: 18,
        height: 18,
        fit: BoxFit.contain,
        color: _isHovered ? Colors.blue : null, // <-- Change color on hover
      ).toWidget(),
    );
  }
}

class _HoverPaginationButton extends StatefulWidget {
  final Icon icon;
  final VoidCallback? onPressed;

  const _HoverPaginationButton({
    required this.icon,
    this.onPressed,
  });

  @override
  State<_HoverPaginationButton> createState() => _HoverPaginationButtonState();
}

class _HoverPaginationButtonState extends State<_HoverPaginationButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          border: Border.all(
            color: widget.onPressed == null
                ? Colors.grey.shade200
                : (isHovered ? Color(0xff0058FF) : Colors.grey.shade300),
            width: 1.0,
          ),
          // No border radius when hovered
          borderRadius: isHovered ? BorderRadius.zero : null,
          color: Colors.white,
        ),
        child: IconButton(
          icon: widget.icon,
          onPressed: widget.onPressed,
          padding: EdgeInsets.zero,
          color: widget.onPressed == null
              ? Colors.grey.shade400
              : (isHovered ? Color(0xff0058FF) : Colors.black),
          constraints: const BoxConstraints(),
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
        ),
      ),
    );
  }
}
