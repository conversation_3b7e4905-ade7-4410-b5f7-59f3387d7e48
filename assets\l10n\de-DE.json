{"app": {"name": "NSL", "version": "Version {version}"}, "common": {"loading": "Wird geladen...", "error": "<PERSON><PERSON>", "success": "Erfolg", "cancel": "Abbrechen", "save": "Speichern", "delete": "Löschen", "edit": "<PERSON><PERSON><PERSON>", "close": "Schließen", "back": "Zurück", "next": "<PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON><PERSON>", "required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "optional": "Optional", "search": "<PERSON><PERSON>", "filter": "Filter", "sort": "<PERSON><PERSON><PERSON><PERSON>", "view": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON><PERSON>", "update": "Aktualisieren", "details": "Details", "noData": "<PERSON><PERSON> ve<PERSON>ü<PERSON>", "retry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ok": "OK", "enterValue": "<PERSON><PERSON> e<PERSON>ben", "pageNotFound": "Seite nicht gefunden", "pageNotFoundMessage": "Die Seite {pageName} wurde nicht gefunden.", "start": "Starten", "notAvailable": "Nicht verfügbar"}, "auth": {"login": "Anmelden", "register": "Registrieren", "logout": "Abmelden", "email": "E-Mail", "password": "Passwort", "confirmPassword": "Passwort bestätigen", "username": "<PERSON><PERSON><PERSON><PERSON>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Nachname", "mobile": "Mobilnummer", "organization": "Organisation", "role": "<PERSON><PERSON>", "fullName": "Vollständiger Name", "rememberMe": "Ang<PERSON><PERSON><PERSON> bleiben", "forgotPassword": "Passwort vergessen?", "welcomeBack": "Willkommen zurück", "pleaseSignIn": "Bitte melden Sie sich an, um fortzufahren", "dontHaveAccount": "Noch kein Konto?", "alreadyHaveAccount": "Bereits ein Konto?", "alreadyRegistered": "Bereits registriert? Klicken Sie hier", "notRegistered": "Noch nicht registriert? Klicken Sie hier", "profilePicture": "Profilbild", "uploadProfilePicture": "<PERSON>il<PERSON><PERSON> ho<PERSON>n", "registrationSuccess": "<PERSON>hr Konto wurde erfolgreich erstellt. <PERSON>te melden Si<PERSON> sich mit Ihren Zugangsdaten an.", "createAccount": "<PERSON><PERSON> er<PERSON>", "signIn": "Anmelden", "signUp": "Registrieren", "resetPassword": "Passwort zurücksetzen", "validation": {"emailRequired": "E-Mail ist erforderlich", "emailInvalid": "Bitte geben Si<PERSON> eine gültige E-Mail-Adresse ein", "passwordRequired": "Passwort ist erforderlich", "passwordTooShort": "Das Passwort muss mindestens 8 Zeichen lang sein", "passwordsDoNotMatch": "Die Passwörter stimmen nicht überein", "usernameRequired": "Benutzername ist erforderlich", "firstNameRequired": "Vorname ist erforderlich", "lastNameRequired": "Nachname ist erforderlich", "organizationRequired": "Organisation ist erforderlich", "roleRequired": "<PERSON>e ist erforderlich", "mobileRequired": "Mobilnummer ist erforderlich", "mobileInvalid": "Bitte geben Si<PERSON> eine gültige Mobilnummer ein"}, "localAccount": "Lokales Konto", "loggingOut": "Wird abgemeldet...", "signOutOfAccount": "<PERSON> abmelden"}, "navigation": {"home": "Startseite", "chat": "Cha<PERSON>", "build": "<PERSON><PERSON><PERSON><PERSON>", "transact": "Transaktion", "myTransactions": "Meine Transaktionen", "settings": "Einstellungen", "profile": "Profil", "logout": "Abmelden", "dashboard": "Dashboard", "components": "Komponenten", "uiComponents": "UI-Komponenten", "drawer": {"appName": "NSL"}, "logoutConfirmation": "Möchten Sie sich wirklich abmelden?", "helpComingSoon": "Hilfebereich kommt in Kürze!", "widgetBinder": "Widget-Binder", "help": "<PERSON><PERSON><PERSON>", "code": "Code"}, "profile": {"title": "Profil", "editProfile": "<PERSON><PERSON>", "changePassword": "Passwort ändern", "personalInfo": "Persönliche Informationen", "contactInfo": "Kontaktinformationen", "saveChanges": "Änderungen speichern", "discardChanges": "Änderungen verwerfen", "userInfoNotAvailable": "Benutzerinformationen nicht verfügbar", "profileInformation": "Profilinformationen", "accountInformation": "Kontoinformationen", "refreshProfile": "Profil aktualisieren", "fullName": "Vollständiger Name", "username": "<PERSON><PERSON><PERSON><PERSON>", "emailAddress": "E-Mail-Adresse", "mobileNumber": "Mobilnummer", "role": "<PERSON><PERSON>", "organization": "Organisation", "userId": "Benutzer-ID", "accountStatus": "<PERSON><PERSON><PERSON><PERSON>", "tenantId": "Mandanten-ID", "roles": "<PERSON><PERSON>", "organizationUnits": "Organisationseinheiten", "authProvider": "Authentifizierungsanbieter", "notProvided": "Nicht angegeben", "notAvailable": "Nicht verfügbar", "editProfileComingSoon": "Profilbearbeitungsfunktion kommt in Kürze", "viewProfileDetails": "<PERSON>hre Profildetails anzeigen"}, "settings": {"title": "Einstellungen", "theme": "Design", "darkMode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "darkModeDescription": "Zwischen hellem und dunklem Design wechseln", "language": "<PERSON><PERSON><PERSON>", "languageDescription": "Sprache der Anwendung ändern", "notifications": "Benachrichtigungen", "account": "Ko<PERSON>", "about": "<PERSON><PERSON>", "help": "<PERSON><PERSON><PERSON>", "feedback": "<PERSON><PERSON><PERSON>", "saveSuccess": "Einstellungen erfolgreich gespeichert", "saveChanges": "Änderungen speichern", "appearance": "Erscheinungsbild", "privacy": "Datenschutz", "advanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uiSettings": "UI-Einstellungen", "chatSettings": "Chat-Einstellungen", "fontSize": "Schriftgröße", "fontSizeDescription": "Textgröße in der gesamten Anwendung anpassen", "uiDensity": "UI-Dichte", "uiDensityDescription": "Abstand zwischen UI-Elementen anpassen", "showTimestamps": "Zeitstempel anzeigen", "showTimestampsDescription": "Zeitstempel für jede Nachricht anzeigen", "showReadReceipts": "Lesebestätigungen anzeigen", "showReadReceiptsDescription": "<PERSON><PERSON><PERSON> mitteilen, wenn Sie ihre Nachrichten gelesen haben", "sendMessageOnEnter": "<PERSON><PERSON><PERSON><PERSON> mit Enter senden", "sendMessageOnEnterDescription": "<PERSON><PERSON> d<PERSON>, um Nachrichten zu senden, anstatt Umschalt+Enter", "version": "Version", "versionDescription": "Aktuelle Anwendungsversion", "light": "Hell", "dark": "<PERSON><PERSON><PERSON>", "system": "System", "comingSoon": "{feature}-Einstellungen kommen in Kürze"}, "dashboard": {"title": "Dashboard", "welcome": "<PERSON><PERSON><PERSON><PERSON>, {name}!", "recentActivity": "Neueste Aktivitäten", "quickActions": "Schnellaktionen", "statistics": "Statistiken", "notifications": "Benachrichtigungen", "viewAll": "Alle anzeigen", "noRecentActivity": "<PERSON><PERSON> neueren Aktivitäten", "noNotifications": "<PERSON><PERSON>"}, "workflow": {"title": "Workflow", "createNew": "Neuen Workflow erstellen", "createNewComingSoon": "Neuen Workflow erstellen - Kommt in Kürze", "workflowDetails": "Workflow-Details", "workflowDetailsComingSoon": "Workflow-Details - Kommt in Kürze", "status": {"active": "Aktiv", "draft": "<PERSON><PERSON><PERSON><PERSON>", "archived": "<PERSON><PERSON><PERSON><PERSON>", "completed": "Abgeschlossen"}, "types": {"approvalWorkflow": "Genehmigungsworkflow", "documentProcessing": "Dokumentenverarbeitung"}, "descriptions": {"approvalWorkflow": "Standardgenehmigungsprozess mit mehreren Schritten", "documentProcessing": "Automatisierte Dokumentenverarbeitung und -validierung"}, "nodeDetails": "Knotendetails", "closeDetails": "Details schließen", "currentSolution": "Aktuelle Lösung", "noActiveSolution": "Keine aktive Lösung", "components": "Komponenten", "noComponentsDefined": "Noch keine Komponenten definiert", "solutionTemplates": "Lösungsvorlagen", "templates": {"dataProcessing": "Datenverarbeitungspipeline", "orderManagement": "Auftragsverwaltungssystem", "customerFeedback": "Kundenfeedback-<PERSON><PERSON><PERSON>"}}, "components": {"title": "UI-Komponenten", "richTextEditor": "<PERSON><PERSON><PERSON>-Text-Editor", "basicEditor": "Einfacher Editor mit Symbolleiste", "tryFormatting": "Versuchen Sie, diesen Text mit der obigen Symbolleiste zu formatieren.", "startTyping": "Beginnen Sie hier zu tippen..."}, "build": {"newSolution": "Neue Lösung", "solutionName": "Lösungsname", "description": "Beschreibung", "uploadFile": "<PERSON><PERSON> ho<PERSON>n", "createSolution": "Lösung erstellen", "create": "<PERSON><PERSON><PERSON><PERSON>", "newProject": "Neues Projekt", "backToCreateMenu": "Zurück zum Erstellungsmenü", "clearChat": "<PERSON><PERSON> l<PERSON>", "clearChatConfirmation": "Möchten Sie wirklich den Chat-Verlauf löschen?", "noSolutions": "<PERSON><PERSON> verfügbar", "createYourFirstSolution": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> Ihre erste Lösung", "solutions": "Lösungen", "solution": "L<PERSON><PERSON><PERSON>", "solutionDetails": "Lösungsdetails", "exportYAML": "YAML exportieren", "suggestions": {"title": "Versuchen Sie, NSL zu fragen:", "createWorkflow": "Workflow erstellen", "generateYAML": "YAML generieren", "buildSolution": "Lösung erstellen"}, "examples": {"createWorkflow": "Workflow zur Verarbeitung von Kundenaufträgen erstellen", "generateYAML": "YAML für eine Datentransformationspipeline generieren", "buildSolution": "Lösung für Bestandsverwaltung erstellen"}}, "chat": {"newChat": "<PERSON><PERSON><PERSON>", "newConversation": "Neue Konversation", "typeMessage": "Nachricht eingeben...", "send": "Senden", "greeting": "Wie kann ich Ihnen helfen {greeting}?", "clearChat": "<PERSON><PERSON> l<PERSON>", "clearChatConfirmation": "Möchten Sie wirklich den Chat-Verlauf löschen?", "cancel": "Abbrechen", "clear": "Löschen", "chatWithNSL": "Mit NSL chatten...", "fetchingAnswer": "NSL sucht nach einer Antwort", "conversations": "Konversationen", "history": "<PERSON><PERSON><PERSON><PERSON>", "searchConversations": "Konversationen durchsuchen", "recentChats": "Neueste Chats", "chatCount": "{count} Chats", "noConversations": "Noch keine Konversationen", "noMessagesYet": "<PERSON>ch keine Nachrichten", "rename": "Umbenennen", "renameConversation": "Konversation umbenennen", "enterNewName": "Neuen Namen e<PERSON>ben", "exportChat": "Chat exportieren", "context": "Kontext", "aiAssistant": "KI-Assistent", "currentConversation": "Aktuelle Konversation", "topic": "Thema:", "noSpecificTopic": "<PERSON><PERSON> spezi<PERSON>s <PERSON><PERSON> er<PERSON>", "keyPoints": "Hauptpunkte:", "noContextAvailable": "• <PERSON><PERSON>ntext für diese Konversation verfügbar", "relatedInformation": "Verwandte Informationen", "noRelatedInformation": "<PERSON><PERSON> verwandten Informationen verfügbar", "microphonePermissionRequired": "Mikrofonberechtigun<PERSON>", "microphonePermissionMessage": "<PERSON>se App benötigt Zugriff auf das Mikrofon für die Text-zu-Sprache-Funktionalität. Bitte erteilen Sie die Mikrofonberechtigung in Ihren Geräteeinstellungen.", "suggestions": {"codeReview": "Hilfe bei der Code-Überprüfung", "explainConcept": "Dieses Ko<PERSON> erklä<PERSON>", "debugCode": "Meinen Code debuggen"}}, "transaction": {"globalObjectives": "Globale Ziele", "localObjectives": "Lokale Ziele", "status": "Status", "id": "ID", "version": "Version", "viewDetails": "Details anzeigen", "startWorkflow": "Workflow starten", "completeWorkflow": "Workflow abschließen", "noObjectives": "<PERSON><PERSON> Ziele verfügbar", "transact": "Transaktion", "myTransactions": "Meine Transaktionen", "checkingExistingTransactions": "Prüfe vorhandene Transaktionen...", "existingTransactions": "Vorhandene Transaktionen", "existingTransactionsFor": "Vorhandene Transaktionen für {name}", "existingTransactionsQuestion": "Es gibt vorhandene Transaktionen. Möchten Sie diese anzeigen?", "noExistingTransactions": "<PERSON>ine vorhandenen Transaktionen gefunden", "startNewTransactionPrompt": "Starten Sie eine neue Transaktion mit der Schaltfläche unten", "startNewTransaction": "Neue Transaktion starten", "instanceId": "Instanz-ID", "created": "<PERSON><PERSON><PERSON><PERSON>", "updated": "<PERSON>ktual<PERSON><PERSON>", "resume": "Fortsetzen", "yes": "<PERSON>a", "no": "<PERSON><PERSON>", "data": "Daten", "timestamp": "Zeitstempel", "clearChat": "<PERSON><PERSON> l<PERSON>", "clearChatConfirmation": "Möchten Sie wirklich den Chat-Verlauf löschen?", "completed": "ABGESCHLOSSEN", "pending": "AUSSTEHEND", "failed": "FEHLGESCHLAGEN", "greeting": "<PERSON><PERSON>, {name}", "welcomeMessage": "Willkommen im Transaktionszentrum", "searchTransactions": "Transaktionen suchen...", "enterTransactionDetails": "Transaktionsdetails eingeben...", "errorLoadingData": "<PERSON><PERSON> beim Laden der Daten: {error}", "objectiveId": "ID: {id}", "objectiveName": "Name: {name}", "objectiveStatus": "Status: {status}", "objectiveVersion": "Version: {version}", "objectiveDetails": "Zieldetails", "startTransaction": "Transaktion starten", "startNewTransactionWith": "Neue Transaktion mit {name} starten?", "errorLoadingTransactions": "Fehler beim Laden der Transaktionen", "loadingTransactions": "Lade Transaktionen...", "noTransactionsFound": "Keine Transaktionen gefunden", "noFilteredTransactionsFound": "Keine {status} Transaktionen gefunden", "all": "Alle", "newTransactionMessage": "Das Formular für neue Transaktionen würde hier erscheinen.", "editTransactionMessage": "Die Transaktionsbearbeitungsfunktion würde hier erscheinen.", "resumeTransactionMessage": "Dies würde zur Workflow-Detailansicht navigieren, um die Transaktion fortzusetzen.", "workerId": "Worker-ID: {id}", "workflowId": "Workflow-ID", "dateTime": "Datum und Uhrzeit", "lastUpdated": "Zuletzt aktualisiert", "updatedDate": "Aktualisiert: {date}", "formattedDate": "{date}", "totalLocalObjectives": "Gesamtzahl lokaler Ziele: {count}", "noLocalObjectiveDetails": "<PERSON><PERSON> Details zu lokalen Zielen verfügbar", "localObjective": "Lokales Ziel", "loCount": "{count} LZ{plural}", "selectItemForDetails": "<PERSON>ählen Sie ein Element aus, um Details anzuzeigen", "globalObjectiveDetails": "Details zu globalen Zielen", "transactionDetails": "Transaktionsdetails", "groupedTransactionDetails": "Gruppierte Transaktionsdetails", "tenantId": "Mandanten-ID", "resumeTransaction": "Transaktion fortsetzen"}, "home": {"typeYourReply": "G<PERSON>en Sie Ihre Antwort ein", "greeting": "Hallo {name}, wie kann ich Ihnen helfen?", "selectQuickMessage": "Bitte wählen Si<PERSON> einen Schnellnachrichtentyp vor dem Senden", "selectQuickMessageHint": "Wählen Sie einen Nachrichtentyp", "sendingMessage": "Na<PERSON><PERSON>t wird gesendet...", "loadingChatHistory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wird geladen...", "noChatHistory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gef<PERSON>en", "askNSL": "NSL fragen", "nsl": "NSL", "solution": "L<PERSON><PERSON><PERSON>", "general": "Allgemein", "internet": "Internet"}, "library": {"books": "12 Bücher", "objects": "102 Objekte", "solutions": "35 Lösungen", "agents": "10 <PERSON><PERSON>", "pageTitle": "Meine Bibliothek", "createButtonText": "<PERSON>uch erstellen"}, "mylibrary": {"buttonText": {"recent": "Neueste", "favourite": "<PERSON><PERSON><PERSON><PERSON>"}, "cardText": {"discover": {"title": "Entdecken", "description": "Teilen Sie Ihre Branche und die Lösungen mit, die Sie benötigen—unsere NSL AI übernimmt die vollständige Lösungsentdeckung und erstellt sie maßgeschneidert für Ihre Bedürfnisse."}, "develop": {"title": "Entwickeln", "description": "Geben Sie Ihre Anforderung ein oder laden <PERSON> sie hoch, und wir werden Ihre Lösung extrahieren, entwickeln und verfeinern mit KI-geführten Vorschlägen während des gesamten Prozesses."}}, "TableHeaderText": {"fileName": "Dateiname", "lastOpened": "Zuletzt geöffnet", "sortWith": "Sortieren nach", "project": "Projekt", "solution": "L<PERSON><PERSON><PERSON>", "object": "Objekt", "role": "<PERSON><PERSON>", "draft": "<PERSON><PERSON><PERSON><PERSON>", "published": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "TableBodyText": {"type": "<PERSON><PERSON>", "draft": "<PERSON><PERSON><PERSON><PERSON>", "published": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "sidemenu": {"chat": "Cha<PERSON>", "create": "<PERSON><PERSON><PERSON><PERSON>", "myBusiness": "<PERSON><PERSON>", "home": "Startseite", "collections": "Sammlungen", "solutions": "Lösungen", "records": "Aufzeichnungen", "myTransactions": "Meine Transaktionen", "calendar": "<PERSON><PERSON><PERSON>", "notifications": "Benachrichtigungen", "nslToJavaCode": "NSL zu Java-Code", "myProfile": "<PERSON><PERSON>", "maxNewPlan": "<PERSON><PERSON>er Max-Plan", "viewPlan": "Plan anzeigen", "learnMore": "<PERSON><PERSON> er<PERSON>", "language": "<PERSON><PERSON><PERSON>", "getHelp": "<PERSON><PERSON><PERSON> erhalten", "settings": "Einstellungen", "logout": "Abmelden"}, "bookdetails": {"book": "<PERSON><PERSON>", "nameOfTheProject": "Name des Projekts", "description": "Beschreibung", "industry": "Branche", "descriptionAboutTheProject": "Beschreibung über das Projekt", "start": "Starten"}, "websolution": {"books": "12 Bücher", "objects": "102 Objekte", "solutions": "35 Lösungen", "pageTitle": "<PERSON><PERSON>", "createButtonText": "Lösung erstellen"}, "webobject": {"pageTitle": "<PERSON><PERSON>", "createButtonText": "Objekt erstellen"}, "webagent": {"pageTitle": "<PERSON><PERSON>", "createButtonText": "Agent <PERSON><PERSON><PERSON>"}}